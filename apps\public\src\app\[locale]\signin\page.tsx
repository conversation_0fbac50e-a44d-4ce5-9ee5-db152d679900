'use client';

import {
  SignInPage as OriginalSignInPage,
  FullPageLoader,
} from '@minicardiac-client/components';
import { useTranslations } from 'next-intl';
import {
  useAuth,
  establishSession,
  auth,
  useVerifySession,
} from '@minicardiac-client/apis';
import { useState, useEffect } from 'react';
import { useSnackbar } from '@minicardiac-client/components';
import { jwtDecode } from 'jwt-decode';

import { useLoading } from '../../../providers/loading-provider';

import { RedirectIfAuthenticated } from '../../../components/protected-route';
import { waitForSessionEstablishment } from '@minicardiac-client/utilities';
import { useRouter } from '../../../i18n/navigation';

export default function SignInPageWrapper() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isProcessingRedirect, setIsProcessingRedirect] = useState(false);
  const [redirectTarget, setRedirectTarget] = useState<string | null>(null);
  const [isReadyToRedirect, setIsReadyToRedirect] = useState(false);

  const { showError } = useSnackbar();
  const t = useTranslations('signin');
  const { hasSeenIntro } = useLoading();
  const sessionQuery = useVerifySession();
  const { signInWithEmailPassword, verifySession, authState } = useAuth();

  useEffect(() => {
    document.title = 'Sign In | MiniCardiac';
  }, []);

  // Move to intro when landing first time on this website
  useEffect(() => {
    const screenWidth = window.innerWidth;
    if (!hasSeenIntro && screenWidth <= 600) {
      router.push('/intro');
    }
  }, [hasSeenIntro, router]);

  const handleNavigate = (path: string) => {
    router.push(path);
  };

  const handleSignIn = async (data: { email: string; password: string }) => {
    setError(null);
    setIsProcessingRedirect(true);
    setRedirectTarget(null);

    try {
      // 1. Firebase Sign In using the auth context
      await signInWithEmailPassword(data.email, data.password);

      // 2. Establish Session
      const sessionEstablished = await establishSession();

      if (!sessionEstablished) {
        throw new Error('Failed to establish session');
      }

      // 3. Verify session and wait for it to complete
      await verifySession();
      await sessionQuery.refetch();

      // Wait for session to be properly established using state checking
      const sessionVerified = await waitForSessionEstablishment();
      if (!sessionVerified) {
        console.warn(
          'Session establishment verification failed, but continuing...'
        );
      }

      const user = auth?.currentUser;
      if (!user) {
        throw new Error('User not found after sign in');
      }

      // Get fresh token with force refresh to ensure it's valid
      const token = await user.getIdToken(true);
      const decodedToken: any = jwtDecode(token || '');

      // Determine the correct redirect path based on user state
      let redirectPath = '';

      if (user && decodedToken.currentStage === 'completed') {
        redirectPath = '/feed';
      } else if (user && !decodedToken.email_verified) {
        redirectPath = `/verify-otp?email=${encodeURIComponent(
          decodedToken.email
        )}&name=${encodeURIComponent(decodedToken.name || '')}&accountType=${
          decodedToken.accountType || 'PUBLIC'
        }`;
      } else {
        // Handle different account types for users in progress
        switch (decodedToken.accountType) {
          case 'PROFESSIONAL':
            if (
              decodedToken.currentStage === 'onboarding:document_upload' ||
              decodedToken.currentStage === 'document_upload'
            ) {
              if (
                decodedToken.subscriptionType === 'paid' ||
                decodedToken.subscriptionPlanTitle === 'Primary' ||
                decodedToken.subscriptionPlanTitle === 'Premium'
              ) {
                redirectPath = '/professional/paid/document-upload';
              } else {
                redirectPath = '/professional/free/profile-setup';
              }
            } else if (
              decodedToken.currentStage === 'onboarding:adding_network' ||
              decodedToken.currentStage === 'adding_network'
            ) {
              if (
                decodedToken.subscriptionType === 'paid' ||
                decodedToken.subscriptionPlanTitle === 'Primary' ||
                decodedToken.subscriptionPlanTitle === 'Premium'
              ) {
                redirectPath = '/professional/paid/add-network';
              } else {
                redirectPath = '/feed?fromSignup=true';
              }
            } else if (
              decodedToken.currentStage === 'onboarding:profile_setup' ||
              decodedToken.currentStage === 'profile_setup'
            ) {
              if (
                decodedToken.subscriptionType === 'paid' ||
                decodedToken.subscriptionPlanTitle === 'Primary' ||
                decodedToken.subscriptionPlanTitle === 'Premium'
              ) {
                redirectPath = '/professional/paid/profile-setup';
              } else {
                redirectPath = '/professional/free/profile-setup';
              }
            } else {
              redirectPath = '/professional/type-selection';
            }
            break;

          case 'ORGANISATION':
            if (
              decodedToken.currentStage === 'onboarding:document_upload' ||
              decodedToken.currentStage === 'document_upload'
            ) {
              if (
                decodedToken.subscriptionType === 'paid' ||
                decodedToken.subscriptionPlanTitle === 'Primary'
              ) {
                redirectPath =
                  '/organisation/subscription/paid/document-upload';
              } else {
                redirectPath =
                  '/organisation/subscription/free/document-upload';
              }
            } else if (
              decodedToken.currentStage === 'onboarding:adding_network' ||
              decodedToken.currentStage === 'adding_network'
            ) {
              if (
                decodedToken.subscriptionType === 'paid' ||
                decodedToken.subscriptionPlanTitle === 'Primary'
              ) {
                redirectPath = '/organisation/subscription/paid/add-network';
              } else {
                redirectPath = '/organisation/subscription/free/add-network';
              }
            } else {
              redirectPath = '/organisation/subscription';
            }
            break;

          case 'PUBLIC':
            if (
              decodedToken.currentStage === 'onboarding:profile_setup' ||
              decodedToken.currentStage === 'profile_setup'
            ) {
              redirectPath = `/patient/profile?name=${encodeURIComponent(
                user?.displayName || ''
              )}`;
            } else if (
              decodedToken.currentStage === 'onboarding:adding_network' ||
              decodedToken.currentStage === 'adding_network'
            ) {
              redirectPath = '/patient/add-network';
            } else {
              redirectPath = `/patient/profile?name=${encodeURIComponent(
                user?.displayName || ''
              )}`;
            }
            break;

          default:
            redirectPath = `/patient/profile?name=${encodeURIComponent(
              user?.displayName || ''
            )}`;
        }
      }

      if (redirectPath) {
        setRedirectTarget(redirectPath);
        setIsReadyToRedirect(true);
      }
    } catch (error: any) {
      showError('Invalid email or password. Please try again.');
      setError('Invalid email or password. Please try again.');
      setIsProcessingRedirect(false);
    }
  };

  const handleForgotPassword = () => {
    // Placeholder for forgot password functionality
  };

  // Handle redirect when ready
  useEffect(() => {
    if (
      redirectTarget &&
      isReadyToRedirect &&
      !authState?.isLoading &&
      !sessionQuery?.isLoading
    ) {
      window.location.href = redirectTarget;
    }
  }, [
    redirectTarget,
    isReadyToRedirect,
    authState.isLoading,
    sessionQuery.isLoading,
  ]);

  return (
    <RedirectIfAuthenticated disableRedirect={isProcessingRedirect}>
      {isProcessingRedirect ? (
        <FullPageLoader open={true} />
      ) : (
        <OriginalSignInPage
          onNavigate={handleNavigate}
          onSignIn={handleSignIn}
          onForgotPassword={handleForgotPassword}
          isLoading={isProcessingRedirect}
          error={error}
          translations={{
            title: t('title'),
            subtitle: t('subtitle'),
            emailLabel: t('emailLabel'),
            passwordLabel: t('passwordLabel'),
            forgotPassword: t('forgotPassword'),
            continueLabel: t('continue'),
            orLabel: t('or'),
            googleLabel: t('google'),
            appleLabel: t('apple'),
            signIn: t('signIn'),
            signUp: t('signUp'),
          }}
        />
      )}
    </RedirectIfAuthenticated>
  );
}
