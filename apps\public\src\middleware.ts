import createMiddleware from 'next-intl/middleware';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { routing } from './i18n/routing';

const intlMiddleware = createMiddleware(routing);

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT || process.env.NODE_ENV;

  // Exclude internal paths and assets
  if (
    pathname.startsWith('/_next') ||
    pathname === '/favicon.ico' ||
    pathname.startsWith('/api/')
  ) {
    return NextResponse.next();
  }

  const firstSegment = pathname.split('/')[1];

  const isPotentialLocale = /^[a-z]{2}$/.test(firstSegment);
  const isInvalidLocale =
    isPotentialLocale && !routing.locales.includes(firstSegment as typeof routing.locales[number]);

  if (isInvalidLocale) {
    const url = request.nextUrl.clone();
    url.pathname = '/404';
    return NextResponse.rewrite(url);
  }

  const isLocalized = routing.locales.includes(firstSegment as typeof routing.locales[number]);
  let response: NextResponse;

  if (isLocalized) {
    response = intlMiddleware(request);
  } else {
    const url = request.nextUrl.clone();
    url.pathname = `/${routing.defaultLocale}${pathname}`;
    return NextResponse.redirect(url);
  }

  if (environment !== 'production') {
    response.headers.set('X-Robots-Tag', 'noindex, nofollow');
  }

  console.log(response);
  return response;
}

export const config = {
  matcher: ['/((?!_next|favicon.ico|api/|.*\\..*).*)'],
};
