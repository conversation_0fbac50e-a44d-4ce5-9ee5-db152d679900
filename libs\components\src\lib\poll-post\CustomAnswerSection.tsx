import { Box, TextField, Typography } from '@mui/material';

interface CustomAnswerSectionProps {
  value: string;
  onChange: (val: string) => void;
  onSubmit: () => void;
}

const CustomAnswerSection = ({
  value,
  onChange,
  onSubmit,
}: CustomAnswerSectionProps) => {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && value.trim()) {
      e.preventDefault();
      onSubmit();
    }
  };

  return (
    <Box
      display="flex"
      alignItems="center"
      position="relative"
      mt="8px"
      width="100%"
    >
      <TextField
        fullWidth
        size="small"
        placeholder="Do you have another suggestion? Type it here!"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        sx={{
          '& .MuiInputBase-root': {
            height: '56px',
            borderRadius: '8px',
            fontSize: '16px',
            fontWeight: 400,
            paddingRight: '60px',
          },
        }}
      />

      {/* Submit Text */}
      <Typography
        onClick={() => value.trim() && onSubmit()}
        sx={{
          position: 'absolute',
          right: '16px',
          fontSize: '14px',
          fontWeight: 600,
          color: value.trim() ? '#A24295' : '#A3A3A3',
          cursor: value.trim() ? 'pointer' : 'default',
          userSelect: 'none',
        }}
      >
        Submit
      </Typography>
    </Box>
  );
};

export default CustomAnswerSection;
