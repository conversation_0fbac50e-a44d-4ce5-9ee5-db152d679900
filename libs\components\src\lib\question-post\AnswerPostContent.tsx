import { Box, Typography, Avatar } from '@mui/material';
import { Comment as CommentType } from '@minicardiac-client/types';

const AnswerPostContent = ({ comment }: { comment: CommentType }) => {
  return (
    <Box
      display="flex"
      gap="12px"
      flexDirection={'column'}
      px={'20px'}
      py={'16px'}
      bgcolor="#F3F4F6"
      borderRadius="8px"
      width={'100%'}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            gap: '4px',
            alignItems: 'center',
          }}
        >
          <Avatar
            src={comment.user.profilePic}
            alt={comment.user.name}
            sx={{ width: 40, height: 40 }}
          />
          <Typography fontSize="16px" fontWeight={600}>
            {comment.user.name}
          </Typography>
        </Box>
        <Typography fontSize="14px" fontWeight={300} color="text.secondary">
          {comment.postedAgo}
        </Typography>
      </Box>
      <Typography mt="8px" fontSize="14px" fontWeight={400}>
        {comment.content}
      </Typography>
    </Box>
  );
};

export default AnswerPostContent;
