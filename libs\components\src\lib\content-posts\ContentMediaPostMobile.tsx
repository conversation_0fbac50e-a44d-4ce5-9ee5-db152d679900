import { useState } from 'react';
import {
  Box,
  IconButton,
  Typography,
  <PERSON>lapse,
  Avatar,
  TextField,
  Divider,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import CustomDialog from '../dialogs/CustomDialog';
import PostFooterActions from './PostFooterActions';
import CommentsList from '../comments/CommentsList';
import PostHeader from './PostHeader';
import { Comment } from '@minicardiac-client/types';

interface ContentMediaPostDialogProps {
  open: boolean;
  onClose: () => void;
  media: string[];
  user: {
    name?: string;
    profilePic?: string;
    postedAgo?: string;
  };
  content?: string;
  likes?: number;
  comments?: number;
  reposts?: number;
  shares?: number;
  commentList?: Comment[];
}

const ContentMediaPostMobile = (props: ContentMediaPostDialogProps) => {
  const {
    open,
    onClose,
    media,
    user,
    content,
    likes = 0,
    comments = 0,
    reposts = 0,
    shares = 0,
    commentList = [],
  } = props;
  const [showDetails, setShowDetails] = useState(false);

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiPaper-root': {
          width: '100%',
          height: '100%',
          maxWidth: 'none',
          margin: 0,
          backgroundColor: '#000',
          borderRadius: '0px',
          maxHeight: 'calc(100% - 54px)',
        },
        p: '0px',
      }}
    >
      {/* Media Image */}
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          height: showDetails ? '284px' : '100%',
          transition: 'height 0.4s',
        }}
      >
        <Box
          component="img"
          src={media[0]}
          alt="Post"
          sx={{ width: '100%', height: '100%', objectFit: 'cover' }}
        />

        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            top: 12,
            left: 12,
            color: '#fff',
          }}
        >
          <ArrowBackIosNewIcon sx={{ fontSize: 20 }} />
        </IconButton>

        {/* Show Details Button */}
        {!showDetails && (
          <Box
            position="absolute"
            bottom={20}
            width="100%"
            display="flex"
            flexDirection="column"
            alignItems="center"
            color="#fff"
            onClick={() => setShowDetails(true)}
            sx={{ cursor: 'pointer' }}
          >
            <ExpandMoreIcon sx={{ fontSize: 28 }} />
            <Typography fontSize={16} fontWeight={500}>
              Details
            </Typography>
          </Box>
        )}
      </Box>

      {/* Slide-Up Content */}
      <Collapse in={showDetails} timeout={400} unmountOnExit>
        <Box
          bgcolor="#fff"
          display="flex"
          flexDirection="column"
          px={'16px'}
          py={'20px'}
        >
          <PostHeader
            user={{
              name: user.name || '',
              profilePic: user.profilePic || '',
              postedAgo: user.postedAgo || '',
            }}
            showOptions
          />

          {/* Caption */}
          <Box
            sx={{
              fontSize: '14px',
              color: '#1E1E1E',
              mt: '20px',
            }}
            dangerouslySetInnerHTML={{ __html: content || '' }}
          />

          <Divider sx={{ mt: '10px', borderColor: '#A3A3A3', opacity: 0.25 }} />

          <PostFooterActions
            likes={likes}
            commentsCount={comments}
            reposts={reposts}
            shares={shares}
          />

          {/* Add Comment */}
          <Box
            display="flex"
            gap="12px"
            alignItems="flex-start"
            mt={'10px'}
            mb={'20px'}
          >
            <Avatar
              src="/placeholder-avatar.png"
              alt="Your profile"
              sx={{ width: 40, height: 40 }}
            />
            <TextField
              fullWidth
              size="small"
              placeholder="What do you think of this post?"
              InputProps={{
                sx: {
                  backgroundColor: '#F3F4F6',
                  borderRadius: '8px',
                  height: '45px',
                  px: 2,
                  fontSize: '14px',
                },
              }}
            />
          </Box>

          {commentList.length > 0 && (
            <Box
              sx={{
                overflowY: 'auto',
                maxHeight: '250px',
                pr: '8px',
              }}
            >
              <CommentsList comments={commentList} />
            </Box>
          )}
        </Box>
      </Collapse>
    </CustomDialog>
  );
};

export default ContentMediaPostMobile;
