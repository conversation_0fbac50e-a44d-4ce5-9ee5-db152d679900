import { useState } from 'react';
import { Box, Button } from '@mui/material';
import { Icon } from '@iconify/react';
import CommentItem from './CommentItem';
import { Comment, CommentsListProps } from '@minicardiac-client/types';

const COMMENTS_PER_PAGE = 5;

const CommentsList = ({
  comments,
  allowPin = false,
  onPin,
}: CommentsListProps & {
  allowPin?: boolean;
  onPin?: (comment: Comment) => void;
}) => {
  const [visibleCount, setVisibleCount] = useState(COMMENTS_PER_PAGE);
  const visibleComments = comments.slice(0, visibleCount);

  const handleShowMore = () =>
    setVisibleCount((prev) => prev + COMMENTS_PER_PAGE);

  return (
    <Box display="flex" flexDirection="column" gap="8px" pr="8px">
      {visibleComments.map((comment) => (
        <CommentItem
          key={comment.id}
          comment={comment}
          allowPin={allowPin}
          onPin={onPin}
        />
      ))}
      {visibleCount < comments.length && (
        <Button onClick={handleShowMore} sx={{ color: '#A24295' }}>
          Show more
          <Icon icon="mdi:chevron-down" width={20} height={20} />
        </Button>
      )}
    </Box>
  );
};

export default CommentsList;
