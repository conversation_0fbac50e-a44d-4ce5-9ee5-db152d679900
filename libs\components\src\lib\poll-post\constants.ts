export interface PollOption {
  id: number | string;
  label: string;
  votes: number;
}

export interface UserInfo {
  name: string;
  profilePic: string;
  postedAgo: string;
}

export const POLL_TITLE = 'This is the Poll Title';

export const WINNING_OPTION = 'Option 2';

export const POLL_CAPTION = `With the growing body of evidence supporting the use of SGLT2 inhibitors in HFpEF patients, are you now routinely prescribing them as part of your initial management plan?`;

export const MOCK_OPTIONS: PollOption[] = [
  { id: 1, label: 'Option 1', votes: 50 },
  { id: 2, label: 'Option 2', votes: 50 },
];

export const CUSTOM_ANSWERS: PollOption[] = [
  { id: 3, label: 'This is custom answer 1', votes: 20 },
  { id: 4, label: 'Interesting custom thought here', votes: 35 },
  { id: 5, label: 'Community contributed insight', votes: 45 },
];

export const POLL_STATS = {
  likes: 600,
  comments: 25,
  reposts: 12,
  shares: 5,
};

export const POLL_USER: UserInfo = {
  name: '<PERSON>',
  profilePic: '/placeholder-avatar.png',
  postedAgo: 'just now',
};
