import { Container, Typography, Box } from '@mui/material';
import { PropsWithChildren } from 'react';

export default function PolicyLayout({
  title,
  children,
}: PropsWithChildren<{ title: string }>) {
  return (
    <Container maxWidth="md" sx={{ py: 6 }}>
      <Typography variant="h3" component="h1" gutterBottom textAlign={'center'}>
        {title}
      </Typography>

      <Typography paragraph>
        This policy was published on [
        <Box component="span" fontWeight="fontWeightBold">
          2025-06-23
        </Box>
        ].
      </Typography>

      <Box component="section" sx={{ typography: 'body1' }}>
        {children}
      </Box>
    </Container>
  );
}
