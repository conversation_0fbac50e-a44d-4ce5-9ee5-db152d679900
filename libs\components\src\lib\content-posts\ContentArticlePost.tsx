import { Box, Divider, Typography } from '@mui/material';
import PostHeader from './PostHeader';
import PostFooterActions from './PostFooterActions';
import { useRouter } from 'next/navigation';

interface ArticlePostProps {
  user?: {
    name: string;
    profilePic: string;
    postedAgo: string;
  };
  articleId?: string;
  title?: string;
  summary?: string;
  content?: string;
  coverImage?: string;
  likes?: number;
  comments?: number;
  reposts?: number;
  shares?: number;
}

const ContentArticlePost = ({
  user = {
    name: '<PERSON>',
    profilePic: '/placeholder-avatar.png',
    postedAgo: 'just now',
  },
  articleId = '7338905434222669825',
  title = 'Cardionex: A Breakthrough in Preventing Recurrent Cardiac Events',
  summary = `Early results from Phase III trials suggest Cardionex, a new oral therapy targeting vascular inflammation, significantly reduces the risk of recurrent heart attacks and major cardiac events. With its unique dual-action approach, Cardionex could soon redefine secondary prevention in cardiology.`,
  content = ``,
  coverImage = '/assets/media-post-images/media-post-1.jpg',
  likes = 150,
  comments = 20,
  reposts = 8,
  shares = 3,
}: ArticlePostProps) => {
  const router = useRouter();

  const articleSlug = `/feed/article/${encodeURIComponent(
    title.toLowerCase().replace(/\s+/g, '-')
  )}-${articleId}`;

  const handleNavigate = () => {
    router.push(articleSlug);
  };

  return (
    <Box
      sx={{
        width: '100%',
        p: { xs: '16px', sm: '20px' },
        borderRadius: '12px',
        backgroundColor: '#fff',
        boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
        boxSizing: 'border-box',
      }}
    >
      {/* Header */}
      <PostHeader user={user} showOptions />

      {/* Main Content */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          gap: '20px',
          mt: '16px',
        }}
      >
        {/* Left: Thumbnail */}
        <Box
          component="img"
          src={coverImage}
          alt="article-thumbnail"
          width={{ xs: '100%', sm: '345px' }}
          height={{ xs: '312px', sm: '220px' }}
          sx={{
            objectFit: 'cover',
            borderRadius: '8px',
            cursor: 'pointer',
          }}
          onClick={handleNavigate}
        />

        {/* Right: Title, Summary, Content */}
        <Box sx={{ flex: 1 }}>
          {/* Title */}
          <Typography
            sx={{
              fontSize: '16px',
              fontWeight: 700,
              color: '#1E1E1E',
              lineHeight: '22px',
            }}
          >
            {title}
          </Typography>

          {/* Summary */}
          <Typography
            sx={{
              fontSize: '12px',
              fontWeight: 400,
              color: '#1E1E1E',
              lineHeight: '20px',
            }}
          >
            {summary}
          </Typography>

          {/* Read More */}
          <Typography
            sx={{
              fontSize: '12px',
              fontWeight: 500,
              color: '#A24295',
              cursor: 'pointer',
              userSelect: 'none',
              display: 'inline-block',
            }}
            onClick={handleNavigate}
          >
            Read the full article →
          </Typography>
        </Box>
      </Box>

      <Divider
        sx={{ mt: '12px', mb: '8px', borderColor: '#A3A3A3', opacity: 0.5 }}
      />

      {/* Footer */}
      <PostFooterActions
        likes={likes}
        commentsCount={comments}
        reposts={reposts}
        shares={shares}
      />
    </Box>
  );
};

export default ContentArticlePost;
