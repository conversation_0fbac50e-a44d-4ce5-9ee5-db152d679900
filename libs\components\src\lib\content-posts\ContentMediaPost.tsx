import { useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import PostFooterActions from './PostFooterActions';
import PostHeader from './PostHeader';
import ContentMediaPostDialog from './ContentMediaPostDialog';
import { ContentMediaImages } from '../media-post/ContentMediaImages';

interface MediaPostProps {
  user?: {
    name: string;
    profilePic: string;
    postedAgo: string;
  };
  content?: string; // HTML content from Tiptap
  media?: string[];
  likes?: number;
  comments?: number;
  reposts?: number;
  shares?: number;
}

const MAX_LINES = 3;

const ContentMediaPost = ({
  user = {
    name: '<PERSON>',
    profilePic: '/placeholder-avatar.png',
    postedAgo: 'just now',
  },
  content = `
<p style="font-weight:500">
  Performed a challenging mitral valve repair today on a patient with severe mitral regurgitation. Precision suturing, careful chordal reconstruction, and intra-operative TEE guidance were key to a successful outcome. Grateful beyond words for the incredible team—anaesthesia, nursing, perfusion, and echo—for their flawless execution and unwavering support throughout. It’s an honour to do what we do. 🫀💪
  </p>
<p style="font-weight:700; color: #1E1E1E;">
  #MitralValveRepair #CardiacSurgery #TeamworkInMedicine #Gratitude #SurgicalLife
</p>
`,
  media = [
    '/assets/media-post-images/media-post-1.jpg',
    '/assets/media-post-images/media-post-2.jpg',
    '/assets/media-post-images/media-post-3.jpg',
    '/assets/media-post-images/media-post-4.jpg',
  ],
  likes = 150,
  comments = 20,
  reposts = 8,
  shares = 3,
}: MediaPostProps) => {
  const [showMore] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);

  return (
    <Box
      sx={{
        width: '100%',
        p: { xs: '16px', sm: '20px' },
        borderRadius: '12px',
        backgroundColor: '#fff',
        boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
        boxSizing: 'border-box',
      }}
    >
      {/* Header */}
      <PostHeader user={user} showOptions={true} />

      {/* Caption */}
      <Box mt={{ xs: '20px', sm: '16px' }} sx={{ position: 'relative' }}>
        <Typography
          component="div"
          sx={{
            fontSize: '12px',
            lineHeight: '18px',
            color: '#1E1E1E',
            display: '-webkit-box',
            WebkitLineClamp: MAX_LINES,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            userSelect: 'text',
          }}
          dangerouslySetInnerHTML={{ __html: content }}
        />
        {!showMore && (
          <Typography
            sx={{
              fontSize: { xs: '12px', sm: '14px' },
              fontWeight: { xs: 600, sm: 500 },
              color: '#A24295',
              cursor: 'pointer',
              userSelect: 'none',
              display: 'inline-block',
              lineHeight: '18px',
            }}
            onClick={() => setOpenDialog(true)}
          >
            See more
          </Typography>
        )}
      </Box>

      {/* Media */}
      <ContentMediaImages media={media} setOpenDialog={setOpenDialog} />
      <Divider
        sx={{ mt: '12px', mb: '8px', borderColor: '#A3A3A3', opacity: 0.5 }}
      />

      {/* Footer */}
      <PostFooterActions
        likes={likes}
        commentsCount={comments}
        reposts={reposts}
        shares={shares}
      />

      <ContentMediaPostDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        media={media}
        user={user}
        content={content}
        likes={likes}
        comments={comments}
        reposts={reposts}
        shares={shares}
      />
    </Box>
  );
};

export default ContentMediaPost;
