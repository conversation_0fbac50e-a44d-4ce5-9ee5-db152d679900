import { Box, TextField, IconButton } from '@mui/material';
import Dr<PERSON><PERSON>and<PERSON> from './DragHandle';
import DeleteIcon from '../../Icons/DeleteIcon';
import { PollOptionInputProps } from './types';

const PollOptionInput = ({
  option,
  onAdd,
  onDelete,
  canDelete,
}: PollOptionInputProps) => (
  <Box display="flex" alignItems="center" gap="8px" width="100%">
    <DragHandle />
    <TextField
      label={option.label}
      placeholder={option.placeholder}
      value={option.value}
      onChange={(e) => onAdd(option.id, e.target.value)}
      fullWidth
      size="small"
    />
    {canDelete && (
      <IconButton onClick={() => onDelete(option.id)}>
        <DeleteIcon fill="#A3A3A3" />
      </IconButton>
    )}
  </Box>
);

export default PollOptionInput;
