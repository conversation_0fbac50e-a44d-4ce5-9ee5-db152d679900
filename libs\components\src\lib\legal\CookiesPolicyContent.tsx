import {
  Box,
  Typography,
  Link,
  List,
  ListItem,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@mui/material';

export function CookiesPolicyContent() {
  return (
    <Box
      sx={{
        '& h2': { mt: 4, mb: 2 },
        '& h3': { mt: 3, mb: 1.5 },
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
      }}
    >
      <Typography paragraph>
        Please read this cookie policy carefully as it contains important
        information on who we are and how we use cookies on our Platform. This
        policy should be read together with our{' '}
        <Link href="/legal/privacy-policy" underline="hover">
          Privacy Policy
        </Link>{' '}
        which sets out how and why we collect, store, use and share personal
        information generally, as well as your rights in relation to your
        personal information and details of how to contact us and supervisory
        authorities if you have a complaint.
      </Typography>

      <Typography paragraph>
        <Box component="span" fontWeight="fontWeightBold">
          Who we are
        </Box>
        <Typography paragraph>
          This Platform is operated by Minicardiac Limited, a company registered
          in England and Wales, with its registered address at 207 Regent
          Street, London, W1B 3HH, United Kingdom.
        </Typography>
      </Typography>

      <Typography paragraph>
        This cookie policy only relates to your use of{' '}
        <Link href="https://minicardiac.com" underline="hover">
          https://minicardiac.com
        </Link>{' '}
        <Box component="span" fontWeight="fontWeightBold">
          (“our website” or “our platform”)
        </Box>
        .
      </Typography>

      <Typography paragraph>
        Throughout our platform we may link to other websites owned and operated
        by certain trusted third parties to make our products and service
        available to you. These other third-party websites may also use cookies
        or similar technologies in accordance with their own separate policies.
        For privacy information relating to these other third-party websites,
        please consult their policies as appropriate.
      </Typography>

      <Typography paragraph>
        <Box component="span" fontWeight="fontWeightBold">
          Consent to use cookies.
        </Box>
        <Typography paragraph>
          We will ask for your consent to place cookies or other similar
          technologies on your device, except where they are essential for us to
          provide you with a service that you have requested (e.g., to enable
          you to use our checkout process).
        </Typography>
      </Typography>

      <Typography paragraph>
        The first time you visit our platform, you will be informed about our
        use of cookies. Consenting to our use of cookies in accordance with the
        terms of this policy when you first visit our platform permits us to use
        cookies every time you visit our platform. However, you may adjust or
        withdraw your consent by using our{' '}
        <Link href="/legal/cookies-settings" underline="hover">
          cookies settings
        </Link>
        , which are accessible at the bottom right of your screen. It may be
        necessary to refresh the page for the updated settings to take effect.
      </Typography>

      <Typography paragraph>
        <Box component="span" fontWeight="fontWeightBold">
          What is a Cookie
        </Box>
        <Typography paragraph>
          A cookie is a small text file which is placed onto your device (e.g.
          computer, smartphone or another electronic device) when you use our
          platform.
        </Typography>
      </Typography>

      <Typography paragraph>We use the following cookies.</Typography>

      <List sx={{ pl: 4, listStyleType: 'disc' }}>
        <ListItem sx={{ display: 'list-item' }}>
          <Box component="span" fontWeight="fontWeightBold">
            Strictly Necessary Cookies
          </Box>{' '}
          - Necessary cookies are essential for the operation of our platform.
          These cookies ensure that basic functions, such as page navigation and
          access to secure areas of the site, are functioning correctly. Without
          these cookies, certain parts of our platform may not operate as
          intended, and we cannot guarantee that the site will be accessible at
          all times.
        </ListItem>

        <ListItem sx={{ display: 'list-item' }}>
          <Box component="span" fontWeight="fontWeightBold">
            Analytical or performance cookies
          </Box>{' '}
          - These allow us to recognise and count the number of visitors and to
          see how visitors move around our platform when they are using it. This
          helps us to improve the way our platform works, for example, by
          ensuring that users are finding what they are looking for easily.
        </ListItem>

        <ListItem sx={{ display: 'list-item' }}>
          <Box component="span" fontWeight="fontWeightBold">
            Functionality cookies
          </Box>{' '}
          – these cookies enable our platform to provide enhanced features and
          personalization for a better user experience. These cookies may be set
          by us or by third-party service providers whose services we have added
          to our site. They allow the website to remember choices you make, such
          as your language preferences, region, or other customizable features.
        </ListItem>

        <ListItem sx={{ display: 'list-item' }}>
          <Box component="span" fontWeight="fontWeightBold">
            Advertisement cookies
          </Box>{' '}
          - These cookies allow us to deliver relevant advertisements to you
          based on your interests. These cookies track your browsing habits
          across different websites and gather information about the pages you
          visit, the links you click, and your overall online behaviour.
        </ListItem>
      </List>

      <Typography paragraph>
        For further information on our use of cookies, including a detailed list
        of your information which we and others may collect through cookies,
        please see the table below.
      </Typography>

      <Typography paragraph>
        <Box component="span" fontWeight="fontWeightBold">
          Our use of cookies
        </Box>
      </Typography>

      <Typography paragraph>
        The table below provides more information about the cookies we use and
        why:
      </Typography>

      <Table>
        <TableHead>
          <TableRow>
            <TableCell>
              <strong>Name</strong>
            </TableCell>
            <TableCell>
              <strong>Purpose</strong>
            </TableCell>
            <TableCell>
              <strong>Type of Cookies</strong>
            </TableCell>
            <TableCell>
              <strong>Duration</strong>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {[1, 2, 3, 4].map((row) => (
            <TableRow key={row}>
              <TableCell>[Insert details]</TableCell>
              <TableCell>[Insert details]</TableCell>
              <TableCell>[Insert details]</TableCell>
              <TableCell>[Insert details]</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <Typography paragraph>
        <Box component="span" fontWeight="fontWeightBold">
          Third party access to the cookies
        </Box>
        <Typography paragraph>
          Please note that the following third parties may also use cookies,
          over which we have no control. These named third parties may include,
          for example, advertising networks and providers of external services
          like web traffic analysis services. These third-party cookies are
          likely to be analytical cookies or performance cookies or targeting
          cookies.
        </Typography>
      </Typography>

      <Typography paragraph>
        <Box component="span" fontWeight="fontWeightBold">
          How to turn off all cookies and the consequences of doing so
        </Box>
        <Typography paragraph>
          If you do not want to accept any cookies, you may be able to change
          your browser settings so that cookies (including those which are
          essential to the services requested) are not accepted. If you do this,
          please be aware that you may lose some of the functionality of our
          platform.
        </Typography>
      </Typography>

      <Typography paragraph>
        For further information about cookies and how to disable them, please
        contact your browser help line or settings.
      </Typography>

      <Typography paragraph>
        <Box component="span" fontWeight="fontWeightBold">
          How to contact us
        </Box>
        <Typography paragraph>
          Please contact us if you have any questions about this cookie policy
          or the information, we hold about you.
          <br />
          If you wish to contact us, please send an email to{' '}
          <Link href="mailto:<EMAIL>" underline="hover">
            <EMAIL>
          </Link>{' '}
        </Typography>
      </Typography>

      <Typography paragraph>
        <Box component="span" fontWeight="fontWeightBold">
          Changes to this policy
        </Box>
        <Typography paragraph>
          We may change this policy from time to time, when we do, we will
          inform you via the email address to which you are provided on our
          platform.
        </Typography>
      </Typography>
    </Box>
  );
}
