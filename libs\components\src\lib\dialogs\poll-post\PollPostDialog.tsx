import { useState } from 'react';
import {
  Box,
  Typography,
  Stack,
  useMediaQuery,
  Menu,
  MenuItem,
} from '@mui/material';
import CustomDialog from '../CustomDialog';
import { LoadingButton } from '../../loading-button';
import { Iconify } from '../../iconify';
import { toast } from 'react-toastify';
import PostToast from '../../toast/PostToast';
import { CustomizedSteppers } from '../../onboarding';
import { BackButton } from '../../buttons/Backbutton';
import { useTheme } from '@emotion/react';
import { PostButton } from '../../buttons/PostButton';
import PollPostDetailsDialog from './PollPostDetailsDialog';
import PollPostForm from './PollPostForm';

interface PollPostDialogProps {
  open: boolean;
  onClose: () => void;
  setOpenScheduleDialog: () => void;
  content: string;
  setContent: (content: string) => void;
}

const DEFAULT_OPTIONS = [
  { id: 1, label: 'Option 1', value: '', placeholder: 'Yes' },
  { id: 2, label: 'Option 2', value: '', placeholder: 'No' },
];

const PollPostDialog = ({
  open,
  onClose,
  setOpenScheduleDialog,
}: PollPostDialogProps) => {
  const [activeStep, setActiveStep] = useState(0);
  const [pollQuestion, setPollQuestion] = useState('');
  const [options, setOptions] = useState(DEFAULT_OPTIONS);
  const [allowCustomAnswers, setAllowCustomAnswers] = useState(false);
  const [caption, setCaption] = useState('');
  const [tags, setTags] = useState('');
  const [audience, setAudience] = useState('PROFESSIONAL');
  const [speciality, setSpeciality] = useState('CARDIAC_SURGERY');
  const [duration, setDuration] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const theme: any = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const filledOptions = options.filter((opt) => opt.value.trim() !== '');
  const isNextEnabled = pollQuestion.trim() && filledOptions.length >= 2;

  const handleNext = () => setActiveStep(activeStep + 1);
  const handleBack = () => setActiveStep(activeStep - 1);

  const canPost = Boolean(caption.trim() && duration);

  const handlePost = () => {
    onClose();
    toast(<PostToast value="Poll Posted" />, {
      position: 'bottom-right',
      autoClose: 5000,
      hideProgressBar: true,
      closeButton: false,
      style: {
        padding: 0,
        width: 'fit-content',
        background: 'white',
        boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)',
      },
    });
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title=""
      sx={{
        p: 0,
        alignItems: { xs: 'stretch', sm: 'start' },
        px: { xs: '0px', sm: '30px', md: '50px', lg: '257px' },
        pt: { xs: 0, sm: '50px' },
        '.MuiDialog-paper': {
          maxHeight: { xs: '100%', sm: 'calc(100% - 64px' },
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        flex={1}
        bgcolor="white"
        height={{ xs: '100%', sm: 642 }}
        mb={{ xs: '100px', sm: '140px', md: '20px' }}
      >
        <Box
          display="flex"
          justifyContent={{ xs: 'space-between', sm: 'center' }}
          alignItems="center"
          paddingX={{ xs: '16px', sm: '40px' }}
          pt={{ xs: '16px', sm: '40px' }}
          pb={'20px'}
        >
          <Box display="flex" gap="16px" alignItems="center" height={'35px'}>
            {screenBelowSM && <BackButton onClick={onClose} />}
            <Typography
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 500,
                fontSize: { xs: '20px', sm: '28px' },
                color: '#1E1E1E',
              }}
            >
              New Poll Post
            </Typography>
          </Box>

          <Typography
            sx={{
              position: 'absolute',
              right: 40,
              top: { xs: 10, sm: 40 },
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 600,
              fontSize: '16px',
              color: '#A24295',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            Drafts <Iconify icon="solar:arrow-right-linear" />
          </Typography>
        </Box>

        <CustomizedSteppers
          activeStep={activeStep}
          steps={['Create Poll', 'Add Details']}
        />

        <Box flex={1} overflow="auto" px={{ xs: '16px', sm: '40px' }}>
          {activeStep === 0 ? (
            <PollPostForm
              pollQuestion={pollQuestion}
              setPollQuestion={setPollQuestion}
              allowCustomAnswers={allowCustomAnswers}
              setAllowCustomAnswers={setAllowCustomAnswers}
              options={options}
              setOptions={setOptions}
            />
          ) : (
            <PollPostDetailsDialog
              caption={caption}
              setCaption={setCaption}
              tags={tags}
              setTags={setTags}
              audience={audience}
              setAudience={setAudience}
              speciality={speciality}
              setSpeciality={setSpeciality}
              duration={duration}
              setDuration={setDuration}
            />
          )}
        </Box>
      </Box>

      <Box
        sx={{
          boxShadow: '0 -4px 20px rgba(0,0,0,0.1)',
          bgcolor: 'white',
          backdropFilter: 'blur(20px)',
          p: '24px',
          position: { xs: 'fixed', sm: 'static' },
          bottom: 0,
          left: 0,
          width: '100%',
          alignItems: 'center',
          display: 'flex',
        }}
      >
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={{ xs: 1, sm: 2 }}
          justifyContent="center"
          alignItems={'center'}
          display={'flex'}
          width={'100%'}
        >
          <LoadingButton
            variant="outlined"
            onClick={activeStep ? handleBack : onClose}
            sx={{
              width: '156px',
              height: '40px',
              fontSize: '16px',
              fontWeight: 700,
              color: '#A24295',
              backgroundColor: 'white',
              borderColor: '#A24295',
              '&:hover': { backgroundColor: 'secondary.light' },
            }}
          >
            Cancel
          </LoadingButton>

          {activeStep === 0 ? (
            <LoadingButton
              onClick={handleNext}
              variant="contained"
              disabled={!isNextEnabled}
              sx={{
                width: '156px',
                height: '40px',
                backgroundColor: isNextEnabled ? '#A24295' : '#ccc',
                color: 'white',
                fontSize: '16px',
                fontWeight: 700,
                '&:hover': {
                  backgroundColor: isNextEnabled ? '#8d2a7b' : '#ccc',
                },
              }}
            >
              Next
            </LoadingButton>
          ) : (
            <PostButton
              setAnchorEl={setAnchorEl}
              handlePost={handlePost}
              disabled={!canPost}
              isOpen={Boolean(anchorEl)}
            />
          )}

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={() => setAnchorEl(null)}
          >
            <MenuItem
              onClick={() => {
                setOpenScheduleDialog();
              }}
            >
              Schedule
            </MenuItem>
            <MenuItem onClick={() => setAnchorEl(null)}>Save Draft</MenuItem>
            <MenuItem onClick={() => setAnchorEl(null)}>
              Add to Sponsorship Queue
            </MenuItem>
          </Menu>
        </Stack>
      </Box>
    </CustomDialog>
  );
};

export default PollPostDialog;
