import { useState, useMemo } from 'react';
import { Box, Typography, useMediaQuery, useTheme } from '@mui/material';
import { Icon } from '@iconify/react';
import CommentIcon from '../Icons/ContentPostIcons/CommentIcon';
import CommentsList from '../comments/CommentsList';
import AddCommentInput from '../comments/AddCommentInput';
import ActionButtonWithCount from './ActionButtonWithCount';
import { Comment } from '@minicardiac-client/types';

interface PostFooterActionsProps {
  likes: number;
  commentsCount: number;
  reposts: number;
  shares: number;
  comments?: Comment[];
  allowPin?: boolean;
  onPin?: (comment: Comment) => void;
  onOpenComments?: () => void;
  showComments?: boolean;
  setShowComments?: (show: boolean) => void;
}

const PostFooterActions = ({
  likes,
  commentsCount,
  reposts,
  shares,
  comments = [],
  allowPin = false,
  onPin,
  onOpenComments,
  showComments = false,
}: PostFooterActionsProps) => {
  const [liked, setLiked] = useState(false);
  const [reposted, setReposted] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const counts = useMemo(
    () => ({ likes, comments: commentsCount, reposts, shares }),
    [likes, commentsCount, reposts, shares]
  );

  return (
    <Box sx={{ width: '100%' }}>
      {/* Action Buttons */}
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        gap="40px"
        mt="20px"
        sx={{ height: '38px', width: '100%' }}
      >
        <ActionButtonWithCount
          icon={
            <Icon
              icon={liked ? 'mdi:heart' : 'mdi:heart-outline'}
              style={{ color: '#A24295', fontSize: 24 }}
            />
          }
          label="Like"
          count={counts.likes}
          onClick={() => setLiked((prev) => !prev)}
        />
        <ActionButtonWithCount
          icon={<CommentIcon />}
          label="Comment"
          count={counts.comments}
          onClick={() => onOpenComments?.()}
        />
        <ActionButtonWithCount
          icon={
            <Icon
              icon={
                reposted
                  ? 'material-symbols:autorenew-rounded'
                  : 'garden:arrow-retweet-stroke-12'
              }
              style={{ color: '#A24295', fontSize: 24 }}
            />
          }
          label="Repost"
          count={counts.reposts}
          onClick={() => setReposted((prev) => !prev)}
        />
        <ActionButtonWithCount
          icon={
            <Icon icon="mdi:share" style={{ color: '#A24295', fontSize: 24 }} />
          }
          label="Share"
          count={counts.shares}
          onClick={() => console.log('Shared')}
        />
      </Box>

      {showComments && (
        <Box
          mt={2}
          display="flex"
          flexDirection="column"
          gap="20px"
          sx={{
            position: isMobile ? 'relative' : 'static',
            height: isMobile ? '100%' : 'auto',
          }}
        >
          {/* Comments List */}
          <Box
            sx={{
              flex: 1,
              overflowY: isMobile ? 'auto' : 'visible',
              p: isMobile ? '16px' : 0,
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}
          >
            {!isMobile && <AddCommentInput />}

            {comments.length > 0 ? (
              <CommentsList
                comments={comments}
                allowPin={allowPin}
                onPin={onPin}
              />
            ) : (
              <Typography
                fontSize="14px"
                fontWeight={500}
                color="text.secondary"
                p={2}
              >
                No comments yet.
              </Typography>
            )}
          </Box>

          {/* Sticky Add Comment Input */}
          {isMobile && (
            <Box
              sx={{
                position: 'fixed',
                bottom: 0,
                left: 0,
                p: '16px',
                backgroundColor: '#fff',
                boxShadow: '0px -4px 20px rgba(0,0,0,0.15)',
                width: '100%',
              }}
            >
              <AddCommentInput />
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

export default PostFooterActions;
