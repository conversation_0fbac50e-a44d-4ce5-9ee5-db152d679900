'use client';

import React from 'react';
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
} from '@mui/material';
import { useRouter } from '@/apps/public/src/i18n/navigation';
import {
  ConnectWithOthers,
  NetworkingPageTemplate,
} from '@minicardiac-client/components';
import {
  // Enhanced hooks with Zustand state
  useGetDiscoverProfilesWithState,
  useSendConnectionRequestWithState,
  useFollowProfileWithState,
  useUnfollowProfileWithState,
  useCancelConnectionRequestWithState,
  useNetworkingStore,
  useAuth,
  useCompleteNetworkingStage,
  refreshSession,
} from '@minicardiac-client/apis';
import { useSnackbar } from '@minicardiac-client/components';

export default function OrganisationPaidAddNetworkPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const { showSuccess, showError } = useSnackbar();

  const { authState } = useAuth();

  // Dialog state
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [dialogAction, setDialogAction] = React.useState<
    'unfollow' | 'cancelRequest'
  >('unfollow');
  const [selectedProfile, setSelectedProfile] = React.useState<{
    id: string;
    name: string;
  } | null>(null);

  // Subscribe to Zustand store changes to force re-renders when connection state changes
  const [connectionRequests, setConnectionRequest] = React.useState(
    useNetworkingStore.getState().connectionRequests
  );
  const [followedProfiles, setFollowedProfiles] = React.useState(
    useNetworkingStore.getState().followedProfiles
  );

  // Set up subscription to Zustand store
  React.useEffect(() => {
    const unsubscribe = useNetworkingStore.subscribe((state) => {
      setConnectionRequest(state.connectionRequests);
      setFollowedProfiles(state.followedProfiles);
    });

    return () => unsubscribe();
  }, []);

  // Fetch profiles using the enhanced API hook with Zustand state
  // This hook automatically enhances profiles with UI state from the Zustand store
  const {
    data: profiles,
    isLoading: isLoadingProfiles,
    error: profilesError,
  } = useGetDiscoverProfilesWithState('ORGANISATION');

  // Enhanced mutation hooks with Zustand state
  const sendConnectionRequest = useSendConnectionRequestWithState();
  const followProfile = useFollowProfileWithState();
  const unfollowProfile = useUnfollowProfileWithState();
  const cancelConnectionRequest = useCancelConnectionRequestWithState();

  // Handle connect button click
  const handleConnect = async (profileId: string) => {
    try {
      setError(null);
      console.log('Connecting to profile with ID:', profileId);

      console.log('Before API call - sending connection request');
      await sendConnectionRequest.mutateAsync(profileId);
      console.log('After API call - connection request sent');

      const currentConnectionRequests =
        useNetworkingStore.getState().connectionRequests;
      console.log(
        'Current Zustand state for connection requests:',
        currentConnectionRequests
      );
      console.log(
        'Is this profile marked as requested?',
        currentConnectionRequests[profileId]
      );

      setConnectionRequest({ ...currentConnectionRequests });

      showSuccess('Connection request sent successfully');
    } catch (err) {
      console.error('Error sending connection request:', err);
      showError('Failed to send connection request');
    }
  };

  const handleFollow = async (profileId: string) => {
    try {
      setError(null);
      console.log('Following profile with ID:', profileId);

      // Make API call - UI state is handled automatically by the enhanced hook
      await followProfile.mutateAsync(profileId);

      const currentFollowedProfiles =
        useNetworkingStore.getState().followedProfiles;
      setFollowedProfiles({ ...currentFollowedProfiles });

      showSuccess('Profile followed successfully');
    } catch (err) {
      console.error('Error following profile:', err);
      showError('Failed to follow profile');
    }
  };

  const handleUnfollow = (profileId: string, profileName: string) => {
    setSelectedProfile({ id: profileId, name: profileName });
    setDialogAction('unfollow');
    setDialogOpen(true);
  };

  const handleCancelRequest = (profileId: string, profileName: string) => {
    setSelectedProfile({ id: profileId, name: profileName });
    setDialogAction('cancelRequest');
    setDialogOpen(true);
  };

  const confirmUnfollow = async () => {
    if (!selectedProfile) return;

    try {
      await unfollowProfile.mutateAsync(selectedProfile.id);

      const currentFollowedProfiles =
        useNetworkingStore.getState().followedProfiles;
      setFollowedProfiles({ ...currentFollowedProfiles });

      showSuccess('Profile unfollowed successfully');
    } catch (err) {
      console.error('Error unfollowing profile:', err);
      showError('Failed to unfollow profile');
    } finally {
      setDialogOpen(false);
      setSelectedProfile(null);
    }
  };

  const confirmCancelRequest = async () => {
    if (!selectedProfile) return;

    try {
      await cancelConnectionRequest.mutateAsync(selectedProfile.id);

      // Force update the local state to trigger a re-render
      const currentConnectionRequests =
        useNetworkingStore.getState().connectionRequests;
      setConnectionRequest({ ...currentConnectionRequests });

      showSuccess('Connection request cancelled successfully');
    } catch (err) {
      console.error('Error cancelling connection request:', err);
      showError('Failed to cancel connection request');
    } finally {
      setDialogOpen(false);
      setSelectedProfile(null);
    }
  };

  const completeNetworkingStage = useCompleteNetworkingStage();

  const handleContinue = async () => {
    setIsSubmitting(true);
    setError(null);

    const sessionRefreshed = await refreshSession();

    if (!sessionRefreshed) {
      console.warn('Failed to refresh session, but will try to continue');
    }

    try {
      await completeNetworkingStage.mutateAsync();

      router.push('/feed?fromSignup=true');
    } catch (err: any) {
      console.error('Error saving network data:', err);
      setError(err.message || 'Failed to save network data');
    } finally {
      setIsSubmitting(false);
    }
  };

  // We don't need to manually enhance profiles anymore as the useGetDiscoverProfilesWithState hook
  // already enhances the profiles with the Zustand store state

  return (
    <NetworkingPageTemplate
      userName={authState.user?.displayName || ''}
      subtitleText="Let's set up your Organisation Account!"
      currentStep={2}
      steps={['Profile Setup', 'Document Upload', 'Adding Network']}
    >
      <ConnectWithOthers
        profiles={profiles}
        isLoading={isLoadingProfiles}
        onConnect={handleConnect}
        onFollow={handleFollow}
        onUnfollow={handleUnfollow}
        onCancelRequest={handleCancelRequest}
        connectionRequests={connectionRequests}
        followedProfiles={followedProfiles}
        hideSteppers={true}
      />

      <Box
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          gap: 2,
          p: { xs: '20px', sm: '36px' },
          bgcolor: 'white',
          zIndex: 1000,
          boxShadow: '0 -4px 10px rgba(0,0,0,0.1)',
        }}
      >
        {/* Confirm and Finish button */}
        <Button
          variant="contained"
          onClick={handleContinue}
          disabled={isSubmitting}
          sx={(theme: any) => ({
            width: '225px',
            height: '40px',
            borderRadius: '8px',
            fontWeight: theme.typography.button.fontWeight,
            fontSize: theme.typography.button.fontSize,
            lineHeight: theme.typography.button.lineHeight,
            textTransform: 'none',
            padding: '0 40px',
            bgcolor: theme.palette.secondary.main,
            color: '#FFFFFF',
            '&:hover': {
              bgcolor: theme.palette.tint.hover,
            },
            '&.Mui-disabled': {
              bgcolor: theme.palette.neutral[500],
            },
          })}
        >
          {isSubmitting ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <CircularProgress size={20} color="inherit" />
              Saving...
            </Box>
          ) : (
            'Confirm and Finish'
          )}
        </Button>
      </Box>

      {(error || profilesError) && (
        <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
          {error || 'Failed to load profiles. Please try again later.'}
        </Box>
      )}

      {/* Confirmation Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        aria-labelledby="confirmation-dialog-title"
      >
        <DialogTitle id="confirmation-dialog-title">
          {dialogAction === 'unfollow'
            ? 'Unfollow this user?'
            : 'Cancel connection request?'}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {dialogAction === 'unfollow'
              ? `Are you sure you want to unfollow Dr. ${selectedProfile?.name}?`
              : `Are you sure you want to cancel your connection request to Dr. ${selectedProfile?.name}?`}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'center', pb: 3, gap: 2 }}>
          <Button
            onClick={() => setDialogOpen(false)}
            variant="outlined"
            sx={{
              width: '136px',
              borderColor: '#A24295',
              color: '#333537',
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={
              dialogAction === 'unfollow'
                ? confirmUnfollow
                : confirmCancelRequest
            }
            variant="contained"
            sx={{
              width: '136px',
              backgroundColor: '#A24295',
              '&:hover': {
                backgroundColor: '#B24E9F',
              },
            }}
          >
            {dialogAction === 'unfollow' ? 'Unfollow' : 'Cancel Request'}
          </Button>
        </DialogActions>
      </Dialog>
    </NetworkingPageTemplate>
  );
}
