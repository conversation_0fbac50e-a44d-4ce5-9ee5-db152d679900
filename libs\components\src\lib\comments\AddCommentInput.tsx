import { Box, Avatar, TextField } from '@mui/material';

const AddCommentInput = () => {
  return (
    <Box display="flex" gap="12px" alignItems="flex-start" mt={2}>
      <Avatar
        src="/placeholder-avatar.png"
        alt="Your profile"
        sx={{ width: 40, height: 40 }}
      />
      <TextField
        fullWidth
        size="small"
        placeholder="What do you think of this post?"
        InputProps={{
          sx: {
            backgroundColor: '#F3F4F6',
            borderRadius: '8px',
            height: '45px',
            px: 2,
            fontSize: '14px',
          },
        }}
      />
    </Box>
  );
};

export default AddCommentInput;
