import { Box, Typography, Avatar } from '@mui/material';
import { Comment as CommentType } from '@minicardiac-client/types';

const ReplyItem = ({ reply }: { reply: CommentType }) => (
  <Box display="flex" gap="12px">
    <Avatar
      src={reply.user.profilePic}
      alt={reply.user.name}
      sx={{ width: 32, height: 32 }}
    />
    <Box flex={1}>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography fontSize="14px" fontWeight={600}>
          {reply.user.name}
        </Typography>
        <Typography fontSize="12px" fontWeight={300} color="text.secondary">
          {reply.postedAgo}
        </Typography>
      </Box>
      <Typography mt="4px" fontSize="13px" fontWeight={400}>
        {reply.content}
      </Typography>
    </Box>
  </Box>
);

export default ReplyItem;
