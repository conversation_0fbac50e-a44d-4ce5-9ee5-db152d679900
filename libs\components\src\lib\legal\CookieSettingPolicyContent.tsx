import {
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Checkbox,
  Link,
} from '@mui/material';
import { useState } from 'react';

export function CookieSettingPolicyContent() {
  const [confirmed, setConfirmed] = useState(false);

  return (
    <Box
      sx={{
        '& h2': { mt: 4, mb: 2 },
        '& h3': { mt: 3, mb: 1.5 },
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
      }}
    >
      <Box>
        <Typography variant="h6" fontWeight="fontWeightBold" gutterBottom>
          Your Privacy is important to Us.
        </Typography>

        <Typography paragraph>
          When you visit a website, it might store or access information on your
          browser, often through cookies. This data could relate to you, your
          preferences, or your device, and is primarily used to ensure the site
          functions as you expect. While this information usually doesn't
          directly identify you, it can help create a more tailored browsing
          experience. Since we value your privacy, you have the option to
          disable certain types of cookies. To learn more or modify the default
          settings, explore the different categories. Keep in mind, though, that
          restricting some cookies may affect your site experience and the
          services we provide. For more information on our use of cookies,
          please see our{' '}
          <Link href="/legal/cookies-policy" underline="hover">
            Cookies Policy
          </Link>
          .
        </Typography>

        <Typography
          variant="h6"
          fontWeight="fontWeightBold"
          gutterBottom
          mt={4}
        >
          Managing your Cookies.
        </Typography>

        <Box mb={2}>
          <Typography fontWeight="fontWeightBold" mb={1}>
            Necessary Cookies
          </Typography>
          <Typography paragraph>
            Necessary cookies are essential for our platform's operation and
            cannot be turned off. While your browser can be configured to notify
            you about or block these cookies, doing so may impact the platform's
            functionality and overall effectiveness for you.
          </Typography>
          {/* Toggle disabled for Necessary Cookies */}
          <Switch checked disabled />
        </Box>

        <Box mb={2}>
          <Typography fontWeight="fontWeightBold" mb={1}>
            Functionality Cookies
          </Typography>
          <Typography paragraph>
            Functionality cookies help us remember your preferences, allowing us
            to customize the platform to suit your needs. Without these cookies,
            certain features of our platform may not be accessible.
          </Typography>
          {/* Toggle for Functionality Cookies */}
          <Switch defaultChecked />
        </Box>

        <Box mb={2}>
          <Typography fontWeight="fontWeightBold" mb={1}>
            Advertisement Cookies
          </Typography>
          <Typography paragraph>
            Advertisement cookies record your visit to our platform, the pages
            you have visited and the links you have followed. We will use this
            information to advertise our services to you.
          </Typography>
          {/* Toggle for Advertisement Cookies */}
          <Switch defaultChecked />
        </Box>

        <Box mb={2}>
          <Typography fontWeight="fontWeightBold" mb={1}>
            Analytical Cookies
          </Typography>
          <Typography paragraph>
            Analytical cookies allow us to recognise and count the number of
            visitors and to see how visitors move around our platform when they
            are using it. This helps us to improve the way our platform works,
            for example, by ensuring that users are finding what they are
            looking for easily.
          </Typography>
          {/* Toggle for Analytical Cookies */}
          <Switch defaultChecked />
        </Box>

        <FormControlLabel
          control={
            <Checkbox
              checked={confirmed}
              onChange={(e) => setConfirmed(e.target.checked)}
              color="primary"
            />
          }
          label="Confirm My Choices"
        />
      </Box>
    </Box>
  );
}
