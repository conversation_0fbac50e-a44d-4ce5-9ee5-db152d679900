'use client';

import { useEffect } from 'react';
import { useAuth, verifySession } from '@minicardiac-client/apis';
import { useQuery } from '@tanstack/react-query';

const useSessionVerification = () => {
  const { authState } = useAuth();

  const sessionQuery = useQuery({
    queryKey: ['auth', 'session'],
    queryFn: async () => {
      if (!authState.isAuthenticated || !authState.user) {
        throw new Error('User not authenticated');
      }
      return await verifySession();
    },
    enabled: authState.isAuthenticated && !!authState.user,
    retry: 3,
    refetchInterval: 10 * 60 * 1000,
    refetchOnWindowFocus: false
  });

  useEffect(() => {
    if (authState.isAuthenticated && !sessionQuery.isLoading && !sessionQuery.isError) {
      sessionQuery.refetch();
    }
  }, [authState.isAuthenticated, sessionQuery]);

  return { isLoading: sessionQuery.isLoading, isError: sessionQuery.isError };
};

export const SessionVerifier = () => {
  useSessionVerification();
  return null;
};

export default SessionVerifier;
