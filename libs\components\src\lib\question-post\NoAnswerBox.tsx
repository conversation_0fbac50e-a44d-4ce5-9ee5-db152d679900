import { Box, Typography } from '@mui/material';

const NoAnswerBox = ({
  title,
  subtitle,
}: {
  title: string;
  subtitle: string;
}) => {
  return (
    <Box
      sx={{
        borderRadius: '8px',
        backgroundColor: '#F3F4F6',
        px: '12px',
        py: '8px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: '20px',
        width: '100%',
      }}
    >
      <Typography fontSize="16px" fontWeight={700}>
        {title}
      </Typography>
      <Typography fontSize="12px" fontWeight={600} color="#A24295">
        {subtitle}
      </Typography>
    </Box>
  );
};

export default NoAnswerBox;
