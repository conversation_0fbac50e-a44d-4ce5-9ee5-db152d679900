'use client';

import React from 'react';
import { Box, Container, useMediaQuery } from '@mui/material';
import { useRouter } from 'next/navigation';
import {
  ProfileSetupForm,
  ProfileFormData,
  PatientProfileWelcome,
  Subtitle,
} from '@minicardiac-client/components';
import { axiosInstance, useAuth } from '@minicardiac-client/apis';
import { useSnackbar } from '@minicardiac-client/components';
import { useTheme } from '@emotion/react';

export default function ProfessionalFreeProfileSetupPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const { showSuccess, showError } = useSnackbar();
  const [formData, setFormData] = React.useState<ProfileFormData>({
    introductoryStatement: '',
    profileImageUrl: '',
    profileImageUrlThumbnail: '',
    title: '',
  });
  const [error, setError] = React.useState<string | null>(null);
  // We need setIsFormValid for the handleFormChange function
  const [, setIsFormValid] = React.useState<boolean>(false);

  const { authState } = useAuth();
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  // Refresh the session when the component mounts
  React.useEffect(() => {
    const refreshUserSession = async () => {
      try {
        // Import dynamically to avoid circular dependencies
        const { refreshSession } = await import('@minicardiac-client/apis');
        await refreshSession();
      } catch (error) {
        console.error('Failed to refresh session:', error);
      }
    };

    refreshUserSession();
  }, []);

  // Handle form changes
  const handleFormChange = (data: ProfileFormData) => {
    setFormData(data);

    // Check if required fields are filled
    const isValid = !!(data.title && data.introductoryStatement);
    setIsFormValid(isValid);
  };

  // Handle "Do this later" button click
  const handleDoThisLater = () => {
    // Navigate to dashboard
    router.push('/feed?fromSignup=true');
  };

  const handleContinue = async () => {
    // Set submitting state
    setIsSubmitting(true);
    setError(null);

    try {
      // Generate a unique identifier for the profile image if none is provided
      const uniqueImageId = Date.now().toString();
      const defaultImageUrl = `default-profile-${uniqueImageId}.jpg`;

      // Post the profile data to the API
      await axiosInstance.post('onboarding/profile-setup/specialist', {
        introductoryStatement: formData.introductoryStatement || '',
        profileImageUrl: formData.profileImageUrl || defaultImageUrl,
        profileImageUrlThumbnail:
          formData.profileImageUrlThumbnail || defaultImageUrl,
        title: formData.title || '',
      });

      // Show success message and navigate
      showSuccess('Profile saved successfully!');
      setTimeout(() => {
        router.push('/feed?fromSignup=true');
      }, 1000);
    } catch (err: any) {
      console.error('Error saving profile data:', err);

      // Check for the specific unique constraint error
      if (
        err.response?.data?.message?.includes(
          'unique constraint "users_profile_image_url_unique"'
        )
      ) {
        const errorMsg =
          'This profile image is already in use. Please choose a different image.';
        setError(errorMsg);
        showError(errorMsg);
      } else {
        setError(err.message || 'Failed to save profile data');
        showError(err.message || 'Failed to save profile data');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add custom CSS to fix the layout
  React.useEffect(() => {
    // Create a style element
    const style = document.createElement('style');

    // Add CSS to fix the Introductory Statement position
    style.innerHTML = `
      /* Move Introductory Statement below Title */
      .MuiGrid-container > .MuiGrid-root:last-child {
        order: 2 !important;
        margin-top: 24px !important;
        width: 100% !important;
      }

      /* Ensure the Introductory Statement has proper width */
      .MuiGrid-container > .MuiGrid-root:last-child > div {
        width: 100% !important;
      }
    `;

    // Append the style to the document head
    document.head.appendChild(style);

    // Clean up on unmount
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <Container maxWidth="lg">
      <Box mt={'20px'}>
        {/* Welcome Header */}
        {!isSmallScreen && (
          <PatientProfileWelcome
            patientName={authState.user?.displayName || ''}
            subtitle={''}
          />
        )}

        <Subtitle
          text={"Let's set up your Professional Account!"}
          sx={{ fontSize: { xs: '12px', sm: '16px' } }}
          marginBottom={'34px'}
        />

        {/* Use the ProfileSetupForm component with isBasicPlan=true */}
        <Box
          sx={{
            mt: { xs: '25px', sm: '30px', md: '40px' },
            py: { xs: 0, sm: '50px' },
            px: { xs: '0px', sm: '30px', md: '74px' },
          }}
        >
          <ProfileSetupForm
            isBasicPlan={true}
            onChange={handleFormChange}
            onSave={handleContinue}
            onSkip={handleDoThisLater}
            isSubmitting={isSubmitting}
          />
          {error && (
            <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
              {error}
            </Box>
          )}
        </Box>
      </Box>
    </Container>
  );
}
