import { useState } from 'react';
import { Box, Typography, Avatar, Divider } from '@mui/material';
import { Comment as CommentType } from '@minicardiac-client/types';
import ActionButton from './ActionButton';
import Dot from './Dot';
import ReplyItem from './ReplyItem';
import AddCommentInput from './AddCommentInput';

const CommentItem = ({
  comment,
  onPin,
  allowPin = false,
}: {
  comment: CommentType;
  onPin?: (comment: CommentType) => void;
  allowPin?: boolean;
}) => {
  const [showReplyInput, setShowReplyInput] = useState(false);

  return (
    <Box display="flex" gap="12px">
      <Avatar
        src={comment.user.profilePic}
        alt={comment.user.name}
        sx={{ width: 40, height: 40 }}
      />
      <Box flex={1} bgcolor="#F3F4F6" p="12px" borderRadius="8px">
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography fontSize="16px" fontWeight={600}>
            {comment.user.name}
          </Typography>
          <Typography fontSize="14px" fontWeight={300} color="text.secondary">
            {comment.postedAgo}
          </Typography>
        </Box>

        <Typography mt="8px" fontSize="14px" fontWeight={400}>
          {comment.content}
        </Typography>

        <Box mt="8px" display="flex" gap="16px" alignItems="center" ml="8px">
          <ActionButton label="Like" />
          <Dot />
          <ActionButton
            label="Reply"
            onClick={() => setShowReplyInput((prev) => !prev)}
          />
          {allowPin && onPin && (
            <>
              <Dot />
              <ActionButton
                label="Pin as Answer"
                onClick={() => onPin(comment)}
              />
            </>
          )}
        </Box>

        {/* Show ReplyInput when Reply clicked */}
        {showReplyInput && <AddCommentInput />}

        {comment.replies && comment.replies.length > 0 && (
          <>
            <Divider sx={{ my: 2, borderColor: '#A3A3A3', opacity: 0.5 }} />
            <Box mt="8px" display="flex" flexDirection="column" gap="8px">
              {comment.replies.map((reply) => (
                <ReplyItem key={reply.id} reply={reply} />
              ))}
            </Box>
          </>
        )}
      </Box>
    </Box>
  );
};

export default CommentItem;
