import React, { createContext, useContext, ReactNode } from 'react';
import { useFirebaseAuth, refreshSession } from './auth-hooks.js';
import {
  AuthState,
  UserData,
  VerifyOtpResponse,
  RegenerateOtpResponse,
  VerifySessionResponse,
} from './types.js';
import { auth } from '../firebase/firebase-client.js';

import {
  signInWithEmailAndPassword as firebaseSignInWithEmailPassword,
  signInWithCustomToken as firebaseSignInWithCustomToken,
  signOut as firebaseSignOut,
} from 'firebase/auth';
import {
  sessionLogin,
  logout,
  registerUser,
  verifyOtp,
  regenerateOtp,
  verifySession,
} from './auth-api.js';

import { jwtDecode } from 'jwt-decode';

// Define the context shape
interface AuthContextType {
  authState: AuthState;

  signInWithEmailPassword: (
    email: string,
    password: string
  ) => Promise<UserData>;
  signInWithCustomToken: (token: string) => Promise<UserData>;

  registerUser: (data: {
    email: string;
    password: string;
    displayName: string;
    accountType: string;
  }) => Promise<{ userId: string; email: string; requiresOTP: boolean }>;

  verifyOtp: (data: {
    email: string;
    otp: string;
  }) => Promise<VerifyOtpResponse>;
  regenerateOtp: (data: { email: string }) => Promise<RegenerateOtpResponse>;

  signOut: () => Promise<void>;

  refreshSession: () => Promise<boolean>;
  verifySession: () => Promise<VerifySessionResponse>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const authState = useFirebaseAuth();

  const signInWithEmailPassword = async (
    email: string,
    password: string
  ): Promise<UserData> => {
    try {
      if (!auth) {
        throw new Error('Firebase auth is not initialized');
      }

      const userCredential = await firebaseSignInWithEmailPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;

      const idToken = await user.getIdToken();

      // Decode the ID token to access custom claims
      const decodedToken: any = jwtDecode(idToken);

      const currentStage = decodedToken?.currentStage;

      // Persist currentStage in localStorage
      if (currentStage) {
        localStorage.setItem('currentStage', currentStage);
      }

      await sessionLogin({ idToken });

      return {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
        currentStage: decodedToken?.currentStage,
      };
    } catch (error: any) {
      console.error('Email/password sign in failed:', error);
      throw new Error(error.message || 'Sign in failed');
    }
  };

  const signInWithCustomToken = async (token: string): Promise<UserData> => {
    try {
      if (!auth) {
        throw new Error('Firebase auth is not initialized');
      }

      const userCredential = await firebaseSignInWithCustomToken(auth, token);
      const user = userCredential.user;

      const idToken = await user.getIdToken();

      await sessionLogin({ idToken });

      return {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
      };
    } catch (error: any) {
      console.error('Custom token sign in failed:', error);
      throw new Error(error.message || 'Sign in with custom token failed');
    }
  };

  const signOut = async (): Promise<void> => {
    try {
      await logout();

      if (auth) {
        await firebaseSignOut(auth);
      }
    } catch (error: any) {
      console.error('Sign out failed:', error);
      throw new Error(error.message || 'Sign out failed');
    }
  };

  const register = async (data: {
    email: string;
    password: string;
    displayName: string;
    accountType: string;
  }) => {
    try {
      return await registerUser(data);
    } catch (error: any) {
      console.error('Registration failed:', error);
      throw new Error(error.message || 'Registration failed');
    }
  };

  const verifyOtpFn = async (data: { email: string; otp: string }) => {
    try {
      return await verifyOtp(data);
    } catch (error: any) {
      console.error('OTP verification failed:', error);
      throw new Error(error.message || 'OTP verification failed');
    }
  };

  const regenerateOtpFn = async (data: { email: string }) => {
    try {
      return await regenerateOtp(data);
    } catch (error: any) {
      console.error('OTP regeneration failed:', error);
      throw new Error(error.message || 'OTP regeneration failed');
    }
  };

  const verifySessionFn = async (): Promise<VerifySessionResponse> => {
    try {
      return await verifySession();
    } catch (error: any) {
      console.error('Session verification failed:', error);
      throw new Error(error.message || 'Session verification failed');
    }
  };

  const contextValue: AuthContextType = {
    authState,
    signInWithEmailPassword,
    signInWithCustomToken,
    registerUser: register,
    verifyOtp: verifyOtpFn,
    regenerateOtp: regenerateOtpFn,
    signOut,
    refreshSession,
    verifySession: verifySessionFn,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};
