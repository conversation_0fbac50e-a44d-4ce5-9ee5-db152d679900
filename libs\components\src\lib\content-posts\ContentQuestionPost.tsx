import { useState } from 'react';
import {
  Box,
  useMediaQuery,
  useTheme,
  Modal,
  Backdrop,
  Fade,
} from '@mui/material';
import PostHeader from '../content-posts/PostHeader';
import PostFooterActions from '../content-posts/PostFooterActions';
import {
  QUESTION_HTML,
  NO_ANSWER_TITLE,
  NO_ANSWER_SUBTITLE,
  QUESTION_STATS,
  QUESTION_USER,
  QUESTION_COMMENTS,
} from '../question-post/questionPostConstants';
import QuestionPostContent from '../question-post/QuestionPostContent';
import NoAnswerBox from '../question-post/NoAnswerBox';
import QuestionAndAnswerIcon from '../Icons/PostIcons/QuestionAndAnswerIcon';
import { Comment } from '@minicardiac-client/types';
import AnswerPostContent from '../question-post/AnswerPostContent';

const ContentQuestionPost = () => {
  const [pinnedAnswer, setPinnedAnswer] = useState<Comment | null>(null);
  const [showCommentsDialog, setShowCommentsDialog] = useState(false);
  const [showComments, setShowComments] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleCommentClick = () => {
    setShowCommentsDialog(true);
    setShowComments((prev) => !prev);
  };

  const handleCloseComments = () => {
    setShowCommentsDialog(false);
    setShowComments(false);
  };

  const PostContent = (
    <Box
      sx={{
        width: '100%',
        p: { xs: '16px', sm: '20px' },
        borderRadius: '8px',
        backgroundColor: '#fff',
        boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
      }}
    >
      <PostHeader user={QUESTION_USER} showOptions />

      <Box display={'flex'} width={'100%'} gap={'16px'}>
        <QuestionAndAnswerIcon
          questionFill={'#A24295'}
          answerFill={'#A3A3A3'}
        />
        <QuestionPostContent html={QUESTION_HTML} />
      </Box>

      <Box display={'flex'} width={'100%'} gap={'16px'}>
        <QuestionAndAnswerIcon />
        {pinnedAnswer ? (
          <AnswerPostContent comment={pinnedAnswer} />
        ) : (
          <NoAnswerBox title={NO_ANSWER_TITLE} subtitle={NO_ANSWER_SUBTITLE} />
        )}
      </Box>

      <PostFooterActions
        likes={QUESTION_STATS.likes}
        commentsCount={QUESTION_STATS.comments}
        reposts={QUESTION_STATS.reposts}
        shares={QUESTION_STATS.shares}
        comments={QUESTION_COMMENTS}
        allowPin
        onPin={setPinnedAnswer}
        onOpenComments={handleCommentClick}
        showComments={showComments}
        setShowComments={setShowComments}
      />
    </Box>
  );

  return (
    <>
      {(!showCommentsDialog || !isMobile) && PostContent}

      {showCommentsDialog && isMobile && (
        <Modal
          open={showCommentsDialog}
          onClose={handleCloseComments}
          closeAfterTransition
          BackdropComponent={Backdrop}
          BackdropProps={{
            timeout: 300,
            sx: { backgroundColor: 'rgba(30, 30, 30, 0.25)' },
          }}
        >
          <Fade in={showCommentsDialog}>
            <Box
              sx={{
                position: 'fixed',
                top: '90px',
                left: 0,
                right: 0,
                bottom: 0,
                bgcolor: '#fff',
                borderTopLeftRadius: '40px',
                borderTopRightRadius: '40px',
                boxShadow: '0px -4px 20px rgba(0, 0, 0, 0.15)',
                display: 'flex',
                flexDirection: 'column',
                p: '16px',
                overflowY: 'auto',
              }}
            >
              {PostContent}
            </Box>
          </Fade>
        </Modal>
      )}
    </>
  );
};

export default ContentQuestionPost;
