import { Dispatch, SetStateAction } from 'react';

export interface PollPostDetailsDialogProps {
  caption: string;
  setCaption: (value: string) => void;
  tags: string;
  setTags: (value: string) => void;
  audience: string;
  setAudience: (value: string) => void;
  speciality: string;
  setSpeciality: (value: string) => void;
  duration: string;
  setDuration: (value: string) => void;
}

export interface PollOption {
  id: string | number;
  label: string;
  placeholder: string;
  value: string;
}

export interface PollOptionInputProps {
  option: PollOption;
  onAdd: (id: string | number, value: string) => void;
  onDelete: (id: string | number) => void;
  canDelete: boolean;
}

export interface PollPostFormProps {
  pollQuestion: string;
  setPollQuestion: (value: string) => void;
  allowCustomAnswers: boolean;
  setAllowCustomAnswers: (value: boolean) => void;
  options: PollOption[];
  setOptions: Dispatch<
    SetStateAction<
      {
        id: number;
        label: string;
        value: string;
        placeholder: string;
      }[]
    >
  >;
}
