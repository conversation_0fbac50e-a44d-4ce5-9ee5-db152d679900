import { useState } from 'react';
import { Box, Typography, Divider, IconButton } from '@mui/material';
import PollOptionCard from '../poll-post/PollOptionCard';
import CustomAnswerList from '../poll-post/CustomAnswerList';
import {
  MOCK_OPTIONS,
  CUSTOM_ANSWERS,
  POLL_TITLE,
  POLL_STATS,
  POLL_USER,
  POLL_CAPTION,
  WINNING_OPTION,
} from '../poll-post/constants';
import PostFooterActions from '../content-posts/PostFooterActions';
import PostHeader from '../content-posts/PostHeader';
import { Iconify } from '../iconify';
import CustomAnswerSection from '../poll-post/CustomAnswerSection';

const MAX_LINES = 2;

const ContentPollPost = () => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [showCustom, setShowCustom] = useState(false);
  const [customAnswer, setCustomAnswer] = useState('');
  const [showMore, setShowMore] = useState(false);

  const totalVotes = [...MOCK_OPTIONS, ...CUSTOM_ANSWERS].reduce(
    (acc, curr) => acc + curr.votes,
    0
  );

  const handleRetractVote = () => setSelectedOption(null);

  return (
    <Box
      sx={{
        width: '100%',
        p: { xs: '16px', sm: '20px' },
        borderRadius: '8px',
        backgroundColor: '#fff',
        boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
        boxSizing: 'border-box',
      }}
    >
      {/* Header */}
      <PostHeader user={POLL_USER} showOptions />

      {/* Caption */}
      <Box mt="16px" sx={{ position: 'relative', width: '100%' }}>
        <Typography
          sx={{
            fontSize: '12px',
            lineHeight: '18px',
            fontWeight: 400,
            color: '#1E1E1E',
            display: '-webkit-box',
            WebkitLineClamp: showMore ? 'unset' : MAX_LINES,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            userSelect: 'text',
          }}
        >
          {POLL_CAPTION}
        </Typography>

        {!showMore && (
          <Typography
            sx={{
              mt: '4px',
              fontSize: '12px',
              lineHeight: '18px',
              fontWeight: 600,
              color: '#A24295',
              cursor: 'pointer',
              userSelect: 'none',
              display: 'inline-block',
            }}
            onClick={() => setShowMore(true)}
          >
            See more
          </Typography>
        )}
      </Box>

      <Box
        sx={{
          border: '1px solid #A3A3A3',
          borderRadius: '8px',
          p: '16px',
          mt: '20px',
        }}
      >
        {/* Poll Title */}
        <Typography
          sx={{
            fontWeight: 600,
            fontSize: '16px',
            lineHeight: '18px',
            my: '16px',
          }}
        >
          {POLL_TITLE}
        </Typography>

        {/* Poll Options */}
        <Box display="flex" flexDirection="column" gap="16px">
          {MOCK_OPTIONS.map((option) => (
            <PollOptionCard
              key={option.label}
              label={option.label}
              votes={option.votes}
              totalVotes={totalVotes}
              selected={selectedOption === option.label}
              onSelect={() => setSelectedOption(option.label)}
            />
          ))}
        </Box>

        {/* Custom Answer Toggle */}
        <Box
          mt="16px"
          textAlign="center"
          sx={{ color: '#A24295', cursor: 'pointer' }}
          onClick={() => setShowCustom(!showCustom)}
        >
          <Typography sx={{ fontWeight: 600, fontSize: '16px' }}>
            Select from Custom Answers
          </Typography>
          <IconButton size="small">
            <Iconify
              icon={showCustom ? 'mdi:chevron-up' : 'mdi:chevron-down'}
              color="#A24295"
            />
          </IconButton>
        </Box>

        {/* Custom Answers */}
        {showCustom && (
          <CustomAnswerList
            answers={CUSTOM_ANSWERS}
            totalVotes={totalVotes}
            selectedOption={selectedOption}
            onSelect={(label) => setSelectedOption(label)}
            onRetract={handleRetractVote}
            winningVote={WINNING_OPTION}
          />
        )}

        {/* Divider */}
        <Divider sx={{ mb: '16px', borderColor: '#A3A3A3', opacity: 0.5 }} />

        {/* Custom Answer Section */}
        <CustomAnswerSection
          value={customAnswer}
          onChange={(val) => setCustomAnswer(val)}
          onSubmit={() => console.log('Submit', customAnswer)}
        />
      </Box>
      {/* Footer Actions */}
      <PostFooterActions
        likes={POLL_STATS.likes}
        commentsCount={POLL_STATS.comments}
        reposts={POLL_STATS.reposts}
        shares={POLL_STATS.shares}
      />
    </Box>
  );
};

export default ContentPollPost;
