import { Box } from '@mui/material';

const QuestionPostContent = ({ html }: { html: string }) => {
  return (
    <Box
      sx={{
        width: '100%',
        color: '#000',
        fontSize: '14px',
        '& h2': { margin: 0, fontSize: '16px', fontWeight: 700 },
        '& p': { margin: 0, fontSize: '12px', fontWeight: 400 },
      }}
      dangerouslySetInnerHTML={{ __html: html }}
    />
  );
};

export default QuestionPostContent;
