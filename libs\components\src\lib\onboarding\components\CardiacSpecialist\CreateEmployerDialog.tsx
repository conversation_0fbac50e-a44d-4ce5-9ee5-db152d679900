import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  TextField,
  Typography,
  CircularProgress,
  FormHelperText,
} from '@mui/material';
import React, { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';

import {
  establishSession,
  refreshSession,
  createEmployer,
} from '@minicardiac-client/apis';
import { auth } from '@minicardiac-client/apis';
import Iconify from '../../../iconify/iconify';
import { useTheme } from '@emotion/react';

const CreateEmployerDialog = ({
  showCreateEmployerModal,
  onClose,
  onSave,
  employerName = '',
}: {
  showCreateEmployerModal: boolean;
  onClose: () => void;
  onSave?: (url: string) => void;
  employerName?: string;
}) => {
  const [url, setUrl] = useState('');
  const [urlError, setUrlError] = useState<string | null>(null);
  const [isValidUrl, setIsValidUrl] = useState(false);

  // Create a mutation for establishing session
  const establishSessionMutation = useMutation({
    mutationFn: async () => {
      console.log('Establishing session...');
      const sessionEstablished = await establishSession();

      if (!sessionEstablished) {
        console.log(
          'Session establishment failed, trying to refresh session...'
        );
        const sessionRefreshed = await refreshSession();

        if (!sessionRefreshed) {
          throw new Error('Your session has expired. Please sign in again.');
        }
      }

      console.log('Session established successfully');
      return true;
    },
  });

  const createEmployerMutation = useMutation({
    mutationFn: async (data: { name: string; url: string }) => {
      console.log('Creating employer with data:', data);

      // First check if we have a valid session
      if (!auth || !auth.currentUser) {
        throw new Error('User not authenticated');
      }

      try {
        console.log('Refreshing Firebase token to get updated claims...');
        await auth.currentUser.getIdToken(true);
        console.log('Firebase token refreshed successfully');
      } catch (error) {
        console.error('Error refreshing token:', error);
      }

      return await createEmployer(data);
    },
    onSuccess: (data) => {
      console.log('Employer created successfully:', data);
      if (onSave && data) {
        // Extract ID from the response or convert to string to fix type error
        onSave(data.id || JSON.stringify(data));
      }
      onClose();
    },
    onError: (
      error: Error & {
        response?: {
          status: number;
          statusText: string;
          data: {
            message?: string;
            [key: string]: unknown;
          };
          headers: Record<string, string>;
        };
        request?: {
          responseURL?: string;
          method?: string;
        };
      }
    ) => {
      console.error('Error creating employer:', error);

      // Log detailed error information for debugging
      console.log('Error details:', {
        message: error.message,
        response: error.response
          ? {
              status: error.response.status,
              statusText: error.response.statusText,
              data: error.response.data,
              headers: error.response.headers,
            }
          : 'No response',
        request: error.request
          ? {
              url: error.request.responseURL,
              method: error.request.method,
            }
          : 'No request',
      });

      // Extract detailed error information
      let errorMessage = 'Unknown error';

      if (error.response) {
        if (error.response.status === 401) {
          errorMessage = 'Authentication required. Please sign in.';
        } else if (error.response.status === 403) {
          errorMessage =
            'You do not have permission to create an employer. This may be a backend permissions issue.';
        } else if (error.response.status === 404) {
          errorMessage =
            'The employer API endpoint was not found. Please check the API URL.';
        } else if (error.response.status === 440) {
          errorMessage = 'Your session expired. Please try again.';
        } else if (error.response.status >= 500) {
          errorMessage = 'Server error. Please try again later.';
        } else if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else {
          errorMessage = `Server error: ${error.response.status} ${error.response.statusText}`;
        }
      } else if (error.request) {
        errorMessage =
          'Network error. Please check your connection and try again.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert(`Error creating employer: ${errorMessage}`);
    },
  });

  // Combined handler for saving
  const handleSave = async () => {
    if (
      establishSessionMutation.isPending ||
      createEmployerMutation.isPending ||
      !isValidUrl
    )
      return;

    // Ensure we have a valid employer name
    const finalEmployerName = employerName.trim();
    if (!finalEmployerName) {
      alert('Please enter a valid employer name');
      return;
    }

    try {
      // First establish a session
      await establishSessionMutation.mutateAsync();

      // Format the URL and create the employer
      const formattedUrl = formatUrl(url);
      const data = {
        name: finalEmployerName,
        url: formattedUrl,
      };

      console.log('Creating employer with data:', data);
      await createEmployerMutation.mutateAsync(data);
    } catch (error) {
      // Errors are handled in the mutation callbacks
      console.error('Error in handleSave:', error);
    }
  };

  // URL validation function
  const validateUrl = (inputUrl: string): boolean => {
    if (!inputUrl) return false;

    try {
      // Add http:// prefix if missing
      const urlToCheck = inputUrl.match(/^https?:\/\//)
        ? inputUrl
        : `https://${inputUrl}`;
      new URL(urlToCheck);
      return true;
    } catch (error) {
      return false;
    }
  };

  // URL change handler with validation
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setUrl(inputValue);

    if (!inputValue) {
      setUrlError('Please enter a website URL');
      setIsValidUrl(false);
      return;
    }

    const isValid = validateUrl(inputValue);
    setIsValidUrl(isValid);
    setUrlError(isValid ? null : 'Please enter a valid website URL');
  };

  // Format URL before submission
  const formatUrl = (inputUrl: string): string => {
    if (!inputUrl) return '';
    return inputUrl.match(/^https?:\/\//) ? inputUrl : `https://${inputUrl}`;
  };

  // Reset URL and validation state when dialog is closed
  useEffect(() => {
    if (!showCreateEmployerModal) {
      setUrl('');
      setUrlError(null);
      setIsValidUrl(false);
    }
  }, [showCreateEmployerModal]);

  const theme: any = useTheme();

  return (
    <Dialog
      open={showCreateEmployerModal}
      onClose={onClose}
      slotProps={{
        paper: {
          component: 'form',
          onSubmit: (event: React.FormEvent<HTMLFormElement>) => {
            event.preventDefault();
            // const formData = new FormData(event.currentTarget);
            // handleClose();
          },
          sx: {
            backgroundColor: 'white',
            paddingY: 3,
            maxWidth: '596px',
          },
        },
      }}
    >
      <DialogTitle sx={{ paddingY: '15px', paddingX: '24px' }}>
        <Typography
          variant="subtitle2"
          sx={{
            lineHeight: 1,
          }}
          component="div"
        >
          Employer not found
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 28,
            color: theme.palette.grey[500],
          }}
        >
          <Iconify
            icon="material-symbols:close-rounded"
            sx={{
              color: 'secondary.main',
              fontSize: '35px',
              width: '35px',
              height: '35px',
            }}
          />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ mt: { xs: '45px', sm: '24px' } }}>
        <DialogContentText>
          <Typography
            variant={'subtitle3' as any}
            sx={{
              color: {
                xs: '#333537',
                sm: (theme) => (theme.palette as any).neutral[500],
              },
              fontWeight: 400,
              fontSize: '16px',
            }}
          >
            {employerName ? `"${employerName}"` : 'Organisation name'} does not
            seem to be registered with MiniCardiac. You can copy a link to their
            website in the space below, if you would like that to appear on your
            profile:
          </Typography>
        </DialogContentText>
        <TextField
          autoFocus
          margin="dense"
          id="name"
          label="Link"
          placeholder="https:// - Enter the link to your website"
          type="url"
          fullWidth
          variant="outlined"
          InputLabelProps={{ shrink: true }}
          value={url}
          onChange={handleUrlChange}
          error={!!urlError && url.length > 0}
          sx={{ mt: { xs: '40px', sm: '24px' } }}
        />
        {urlError && url.length > 0 && (
          <FormHelperText error>{urlError}</FormHelperText>
        )}
      </DialogContent>
      <DialogActions
        sx={{
          justifyContent: 'center',
        }}
      >
        <Button
          onClick={onClose}
          variant="outlined"
          sx={(theme) => ({
            width: '136px',
            borderColor: theme.palette.secondary.main,
          })}
        >
          <Typography variant={'professionalType' as any} color="secondary">
            Cancel
          </Typography>
        </Button>
        <Button
          onClick={handleSave}
          disabled={
            !isValidUrl ||
            establishSessionMutation.isPending ||
            createEmployerMutation.isPending
          }
          variant="contained"
          type="button"
          sx={(theme) => ({
            width: '136px',
            bgcolor: isValidUrl
              ? theme.palette.secondary.main
              : (theme.palette as any)?.neutral?.[500],
            '&.Mui-disabled': {
              bgcolor: (theme.palette as any)?.neutral?.[500],
            },
          })}
        >
          {establishSessionMutation.isPending ||
          createEmployerMutation.isPending ? (
            <CircularProgress size={24} color="inherit" />
          ) : (
            <Typography
              variant={'professionalType' as any}
              sx={{
                color: 'white',
              }}
            >
              Save
            </Typography>
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CreateEmployerDialog;
