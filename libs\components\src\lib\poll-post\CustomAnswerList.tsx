import { Box, Typography } from '@mui/material';
import PollOptionCard from './PollOptionCard';
import { PollOption } from './constants';
import MessageIcon from '../Icons/PostIcons/MessageIcon';

interface Props {
  answers: PollOption[];
  totalVotes: number;
  selectedOption: string | null;
  onSelect: (label: string) => void;
  onRetract: () => void;
  winningVote: string;
}

const CustomAnswerList = ({
  answers,
  totalVotes,
  selectedOption,
  onSelect,
  onRetract,
  winningVote,
}: Props) => {
  return (
    <Box display="flex" flexDirection="column" gap="16px" mt="16px">
      {answers.map((answer) => (
        <PollOptionCard
          key={answer.id}
          label={answer.label}
          votes={answer.votes}
          totalVotes={totalVotes}
          selected={selectedOption === answer.label}
          onSelect={() => onSelect(answer.label)}
          winningVote={winningVote}
        >
          <Box display="flex" alignItems="center" gap="8px">
            <MessageIcon />
            <Typography sx={{ fontSize: '16px', fontWeight: 400 }}>
              {answer.label}
            </Typography>
          </Box>
        </PollOptionCard>
      ))}

      {/* Retract Vote */}
      <Typography
        onClick={onRetract}
        sx={{
          color: '#A24295',
          fontWeight: 600,
          fontSize: '16px',
          cursor: 'pointer',
          mt: '8px',
          textAlign: 'center',
        }}
      >
        Retract Vote
      </Typography>
    </Box>
  );
};

export default CustomAnswerList;
