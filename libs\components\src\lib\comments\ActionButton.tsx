import { Button } from '@mui/material';

const ActionButton = ({
  label,
  onClick,
}: {
  label: string;
  onClick?: () => void;
}) => (
  <Button
    onClick={onClick}
    variant="text"
    sx={{
      p: 0,
      minWidth: 'auto',
      fontSize: '12px',
      fontWeight: 600,
      color: '#A24295',
      textTransform: 'none',
    }}
  >
    {label}
  </Button>
);

export default ActionButton;
