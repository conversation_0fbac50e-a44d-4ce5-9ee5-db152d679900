import {
  Box,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
} from '@mui/material';

const DurationDropdown = ({
  duration,
  setDuration,
}: {
  duration: string;
  setDuration: (value: string) => void;
}) => {
  const handleChange = (event: SelectChangeEvent) => {
    setDuration(event.target.value);
  };

  return (
    <FormControl
      sx={{
        width: { xs: '100%', sm: '320px' },
        height: '45px',
      }}
    >
      <InputLabel
        shrink
        id="duration-label"
        sx={{
          fontSize: '16px',
          fontWeight: 500,
          color: '#000',
          '&.Mui-focused': {
            color: '#A24295',
          },
          backgroundColor: 'white',
          px: '10px',
        }}
      >
        Duration
      </InputLabel>
      <Select
        labelId="duration-label"
        value={duration}
        onChange={handleChange}
        displayEmpty
        sx={{
          width: { xs: '100%', sm: '320px' },
          height: '45px',
          fontSize: '16px',
          color: duration ? '#1E1E1E' : '#A3A3A3',
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: '#A24295',
          },
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: '#A3A3A3',
          },
        }}
        renderValue={(selected) => {
          if (!selected) {
            return (
              <Box sx={{ color: '#A3A3A3', fontSize: '16px', fontWeight: 400 }}>
                Set a duration for your poll
              </Box>
            );
          }
          return selected;
        }}
      >
        <MenuItem value="24 hours">24 hours</MenuItem>
        <MenuItem value="3 days">3 days</MenuItem>
        <MenuItem value="1 week">1 week</MenuItem>
      </Select>
    </FormControl>
  );
};

export default DurationDropdown;
