import {
  Box,
  <PERSON>ack,
  TextField,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import PollOptionInput from './PollOptionInput';
import { PollPostFormProps } from './types';

const PollPostForm = ({
  pollQuestion,
  setPollQuestion,
  allowCustomAnswers,
  setAllowCustomAnswers,
  options,
  setOptions,
}: PollPostFormProps) => {
  const handleOptionChange = (id: string | number, value: string) => {
    setOptions((prev) =>
      prev.map((opt) => (opt.id === id ? { ...opt, value } : opt))
    );
  };

  const handleDeleteOption = (id: string | number) => {
    setOptions((prev) => prev.filter((opt) => opt.id !== id));
  };

  const handleAddOption = () => {
    setOptions((prev) => [
      ...prev,
      {
        id: Date.now(),
        label: `Option ${prev.length + 1}`,
        value: '',
        placeholder: `Option ${prev.length + 1}`,
      },
    ]);
  };

  return (
    <Box>
      <Box mb="40px" mt="40px">
        <TextField
          label="Poll Question"
          placeholder="Got a question to put to your audience? Ask it here."
          fullWidth
          multiline
          minRows={3}
          value={pollQuestion}
          InputLabelProps={{ shrink: true }}
          onChange={(e) => setPollQuestion(e.target.value)}
          sx={{
            '& .MuiOutlinedInput-root': {
              height: { xs: 200, sm: 102 },
              alignItems: 'start',
              '& textarea': {
                height: '100% !important',
                boxSizing: 'border-box',
                resize: 'none',
                overflowY: 'auto',
                '&::placeholder': {
                  textAlign: 'left',
                  verticalAlign: 'top',
                  lineHeight: 1.2,
                },
              },
            },
          }}
        />
      </Box>

      <Stack spacing="33px" mb="20px">
        {options.map((option) => (
          <PollOptionInput
            key={option.id}
            option={option}
            onAdd={handleOptionChange}
            onDelete={handleDeleteOption}
            canDelete={true}
          />
        ))}
        <PollOptionInput
          option={{
            id: 'add',
            label: '',
            value: '',
            placeholder: 'Add another option',
          }}
          onAdd={handleAddOption}
          onDelete={handleDeleteOption}
          canDelete={false}
        />
      </Stack>

      <FormControlLabel
        control={
          <Checkbox
            checked={allowCustomAnswers}
            onChange={(e) => setAllowCustomAnswers(e.target.checked)}
            sx={{
              color: '#A3A3A3',
              '&.Mui-checked': { color: '#A24295' },
            }}
          />
        }
        label="Allow custom answers"
      />
    </Box>
  );
};

export default PollPostForm;
