import { memo } from 'react';
import { Box, Typography } from '@mui/material';

interface ActionButtonWithCountProps {
  icon: React.ReactNode;
  label: string;
  count: number;
  onClick: () => void;
}

const ActionButtonWithCount = memo(
  ({ icon, label, count, onClick }: ActionButtonWithCountProps) => (
    <Box
      display="flex"
      alignItems="center"
      justifyContent="center"
      gap="4px"
      sx={{ cursor: 'pointer' }}
      onClick={onClick}
    >
      {icon}
      <Typography
        fontSize="12px"
        fontWeight={600}
        color="#A24295"
        sx={{ display: { xs: 'none', sm: 'inline' } }}
      >
        {label} ({count})
      </Typography>
      <Typography
        fontSize="12px"
        fontWeight={600}
        color="#A24295"
        sx={{ display: { xs: 'inline', sm: 'none' } }}
      >
        {count}
      </Typography>
    </Box>
  )
);

export default ActionButtonWithCount;
