const DeleteIcon = ({ size = 24, fill = 'white', ariaLabel = 'Delete' }) => {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label={aria<PERSON>abel}
    >
      <path
        d="M7 11.2632C7 10.6824 7.414 10.2108 7.9252 10.2108H11.1232C11.758 10.1916 12.3184 9.732 12.5344 9.0528L12.5704 8.9328L12.7084 8.4636C12.7924 8.1756 12.8656 7.9236 12.9688 7.6992C13.3744 6.8124 14.1256 6.1968 14.9932 6.0396C15.214 6 15.4468 6 15.7132 6H19.8868C20.1544 6 20.3872 6 20.6068 6.0396C21.4744 6.1968 22.2268 6.8124 22.6312 7.6992C22.7344 7.9236 22.8076 8.1744 22.8928 8.4636L23.0296 8.9328L23.0656 9.0528C23.2816 9.732 23.9536 10.1928 24.5896 10.2108H27.6736C28.186 10.2108 28.6 10.6824 28.6 11.2632C28.6 11.844 28.186 12.3156 27.6748 12.3156H7.924C7.414 12.3156 7 11.844 7 11.2632Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.3152 30H18.2848C21.6244 30 23.2936 30 24.3808 28.9368C25.4656 27.8736 25.576 26.13 25.798 22.6428L26.1184 17.6172C26.2384 15.7248 26.2984 14.778 25.7548 14.1792C25.21 13.5792 24.2908 13.5792 22.4512 13.5792H13.1488C11.3092 13.5792 10.3888 13.5792 9.8452 14.1792C9.3004 14.7792 9.3604 15.7248 9.4816 17.6172L9.802 22.6428C10.024 26.13 10.1344 27.8748 11.2204 28.9368C12.3064 30 13.9756 30 17.3152 30ZM15.6952 18.2268C15.6472 17.706 15.2056 17.3268 14.7112 17.3784C14.2156 17.43 13.8556 17.8944 13.9048 18.4152L14.5048 24.7308C14.5528 25.2516 14.9944 25.6308 15.4888 25.5792C15.9844 25.5276 16.3444 25.0632 16.2952 24.5424L15.6952 18.2268ZM20.89 17.3784C21.3844 17.43 21.7456 17.8944 21.6952 18.4152L21.0952 24.7308C21.0472 25.2516 20.6044 25.6308 20.1112 25.5792C19.6156 25.5276 19.2556 25.0632 19.3048 24.5424L19.9048 18.2268C19.9528 17.706 20.3968 17.3268 20.89 17.3784Z"
        fill={fill}
      />
    </svg>
  );
};

export default DeleteIcon;
