import { Box, Typography } from '@mui/material';

interface PollOptionCardProps {
  label: string;
  votes: number;
  totalVotes: number;
  selected: boolean;
  onSelect: () => void;
  winningVote?: string;
  children?: React.ReactNode;
}

const PollOptionCard = ({
  label,
  votes,
  totalVotes,
  selected,
  onSelect,
  winningVote,
  children,
}: PollOptionCardProps) => {
  const percentage =
    totalVotes > 0 ? ((votes / totalVotes) * 100).toFixed(0) : '0';
  const isWinning = label === winningVote;

  const getFilledBackgroundColor = (
    selected: boolean,
    isWinning: boolean
  ): string => {
    if (selected) {
      return isWinning ? '#F6ECF4' : '#A3A3A375';
    }
    return 'transparent';
  };

  const filledBackgroundColor = getFilledBackgroundColor(selected, isWinning);

  const typographyWeight = selected && isWinning ? 700 : 400;

  return (
    <Box
      onClick={onSelect}
      sx={{
        position: 'relative',
        height: '56px',
        borderRadius: '8px',
        border: '1px solid #A24295',
        overflow: 'hidden',
        cursor: 'pointer',
      }}
    >
      {/* Filled Background */}
      <Box
        sx={{
          position: 'absolute',
          height: '100%',
          width: `${selected ? percentage : 0}%`,
          backgroundColor: filledBackgroundColor,
          transition: 'width 0.3s, background-color 0.3s',
        }}
      />

      {/* Option Content */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          height: '100%',
          px: '16px',
          position: 'relative',
          zIndex: 1,
        }}
      >
        <Typography
          sx={{
            fontSize: '16px',
            fontWeight: typographyWeight,
            color: '#1E1E1E',
          }}
        >
          {children || label}
        </Typography>
        <Typography
          sx={{ fontSize: '16px', fontWeight: 400, color: '#1E1E1E' }}
        >
          {selected ? `${percentage}%` : ''}
        </Typography>
      </Box>
    </Box>
  );
};

export default PollOptionCard;
