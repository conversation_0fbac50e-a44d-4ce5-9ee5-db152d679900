import { useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';

import PostFooterActions from './PostFooterActions';
import PostHeader from './PostHeader';

interface UserInfo {
  name: string;
  profilePic: string;
  postedAgo: string;
}

interface TextPostProps {
  user?: UserInfo;
  content?: string;
  likes?: number;
  comments?: number;
  reposts?: number;
  shares?: number;
}

const MAX_LINES = 6;

const ContextTextPost = ({
  user = {
    name: '<PERSON>',
    profilePic: '/placeholder-avatar.png',
    postedAgo: 'just now',
  },
  content = `
<p style="font-size:16px; font-weight:500; margin-bottom:16px;">
  Rethinking Our Approach to HFpEF
</p>

<p style="font-size:12px; font-weight:400; margin-bottom:16px;">
  Lately, I’ve been reflecting on the evolving landscape of heart failure with preserved ejection fraction (HFpEF). Despite increasing recognition and clinical focus, we still lack robust, targeted therapies that consistently improve long-term outcomes.
</p>

<p style="font-size:12px; font-weight:400; margin-bottom:16px;">
  With more patients—especially older adults and women—being diagnosed, are we doing enough to stratify risk early and personalize management strategies? The recent guidelines emphasize comorbidity optimization, but we need more large-scale trials to refine treatment algorithms.
</p>

<p style="font-size:12px; font-weight:400; margin-bottom:16px;">
  Curious to hear how others are managing these cases in real-world practice. Are you integrating newer agents like SGLT2 inhibitors routinely? How do you handle the diagnostic gray zones?
</p>

<p style="font-size:12px; font-weight:700;">
  Let’s keep the conversation going—we learn the most from each other. #Cardiology #HeartFailure #HFpEF #ClinicalPractice #CardioCommunity
</p>`,
  likes = 600,
  comments = 25,
  reposts = 12,
  shares = 5,
}: TextPostProps) => {
  const [showMore, setShowMore] = useState(false);

  return (
    <Box
      sx={{
        width: '100%',
        p: '20px',
        borderRadius: '12px',
        backgroundColor: '#fff',
        boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
        boxSizing: 'border-box',
      }}
    >
      {/* Header */}
      <PostHeader user={user} showOptions={true} />

      {/* Content */}
      <Box mt="16px" sx={{ position: 'relative', width: '100%' }}>
        <Typography
          component="div"
          sx={{
            fontSize: '14px',
            lineHeight: '18px',
            fontWeight: 400,
            color: '#1E1E1E',
            display: '-webkit-box',
            WebkitLineClamp: showMore ? 'unset' : MAX_LINES,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            userSelect: 'text',
          }}
          dangerouslySetInnerHTML={{ __html: content }}
        />

        {!showMore && (
          <Typography
            sx={{
              mt: '4px',
              fontSize: '14px',
              fontWeight: 500,
              color: '#A24295',
              cursor: 'pointer',
              userSelect: 'none',
              display: 'inline-block',
            }}
            onClick={() => setShowMore(true)}
          >
            See more
          </Typography>
        )}
      </Box>
      <Divider
        sx={{ mt: '12px', mb: '8px', borderColor: '#A3A3A3', opacity: 0.5 }}
      />

      {/* Footer Actions with icons + labels */}
      <PostFooterActions
        likes={likes}
        commentsCount={comments}
        reposts={reposts}
        shares={shares}
      />
    </Box>
  );
};

export default ContextTextPost;
