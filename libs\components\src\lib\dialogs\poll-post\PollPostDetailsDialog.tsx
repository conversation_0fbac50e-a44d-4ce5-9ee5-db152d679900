import { Box, TextField } from '@mui/material';
import CustomToggleButtonGroup from '../../buttons/CustomToggleButtonGroup';
import DurationDropdown from './DurationDropdown';
import { PollPostDetailsDialogProps } from './types';

const PollPostDetailsDialog = ({
  caption,
  setCaption,
  tags,
  setTags,
  audience,
  setAudience,
  speciality,
  setSpeciality,
  duration,
  setDuration,
}: PollPostDetailsDialogProps) => {
  return (
    <Box display="flex" flexDirection="column" flex={1}>
      <Box mt={{ xs: '20px', sm: '40px' }} mb="40px">
        <TextField
          placeholder="Curious to see how clinical practice is evolving—thanks for participating!"
          value={caption}
          onChange={(e) => setCaption(e.target.value)}
          fullWidth
          multiline
          minRows={3}
          InputLabelProps={{ shrink: true }}
          label="Caption"
          sx={{
            '& .MuiOutlinedInput-root': {
              height: { xs: 181, sm: 136 },
              alignItems: 'start',
              '& textarea': {
                height: '100% !important',
                boxSizing: 'border-box',
                resize: 'none',
                overflowY: 'auto',
                '&::placeholder': {
                  textAlign: 'left',
                  verticalAlign: 'top',
                  lineHeight: 1.2,
                },
              },
            },
          }}
        />
      </Box>

      <Box
        display="flex"
        flexDirection={{ xs: 'column' }}
        gap={{ xs: '20px', lg: '20px' }}
        alignItems={{ xs: 'center', md: 'end' }}
        mb="40px"
      >
        <TextField
          placeholder="#Surgery #Research"
          value={tags}
          onChange={(e) => setTags(e.target.value)}
          fullWidth
          label="Tags"
          InputLabelProps={{ shrink: true }}
        />
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: '20px',
            width: '100%',
          }}
        >
          <Box width={{ xs: '100%', md: '224px' }}>
            <CustomToggleButtonGroup
              label="Community"
              options={['PROFESSIONAL', 'PUBLIC', 'BOTH']}
              selected={audience}
              onChange={setAudience}
              width={{ xs: '100%', md: '224px' }}
            />
          </Box>
          <Box width={{ xs: '100%', md: '282px' }}>
            <CustomToggleButtonGroup
              label="Audience"
              options={['CARDIAC_SURGERY', 'CARDIOLOGY', 'BOTH']}
              selected={speciality}
              onChange={setSpeciality}
              width={{ xs: '100%', md: '282px' }}
            />
          </Box>
        </Box>
      </Box>

      <Box mb="40px">
        <DurationDropdown duration={duration} setDuration={setDuration} />
      </Box>
    </Box>
  );
};

export default PollPostDetailsDialog;
