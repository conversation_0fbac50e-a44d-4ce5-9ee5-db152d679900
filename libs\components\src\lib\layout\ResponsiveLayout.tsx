'use client';
import { Box, useMediaQuery } from '@mui/material';
import MobileTopBar from './MobileTopBar';
import BottomNavBar from './BottomNavBar';
import TopBar from './TopBar';
import MainContent from './MainContent';
import RightSidebar from './RightSidebar';
import Sidebar from './Sidebar';
import ContentMediaPost from '../content-posts/ContentMediaPost';
import ContentArticlePost from '../content-posts/ContentArticlePost';
import React from 'react';
import ContextTextPost from '../content-posts/ContentTextPost';
import { MobileSearchbar } from './MobileSearchBar';
import { useTheme } from '@emotion/react';
import ContentPollPost from '../content-posts/ContentPollPost';
import ContentQuestionPost from '../content-posts/ContentQuestionPost';

export default function ResponsiveLayout() {
  const theme: any = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        height: '100vh',
        width: '100%',
        justifyContent: 'center',
        backgroundColor: '#F3F4F6',
        position: 'relative',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          gap: { xs: '0px', md: '20px', lg: '40px' },
          width: { xs: '100%', lg: '1280px' },
        }}
      >
        {/* Sidebar - Desktop Only */}
        {!isMobile && (
          <Box
            sx={{
              height: '100vh',
              overflowY: 'auto',
              backgroundColor: 'white',
              width: { sm: 100, xs: 100, md: 224 },
              minWidth: { sm: 100, xs: 100, md: 224 },
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              '&::-webkit-scrollbar': {
                display: 'none',
              },
            }}
          >
            <Sidebar />
          </Box>
        )}

        {/* Main Section */}
        <Box
          sx={{
            display: 'flex',
            flex: 1,
            flexDirection: 'column',
            overflow: 'hidden',
            gap: '20px',
            position: 'relative',
          }}
        >
          {/* Top Bars */}
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              zIndex: 10,
              width: '100%',
              backgroundColor: 'transparent',
            }}
          >
            {isMobile ? (
              <>
                <MobileTopBar />
                <MobileSearchbar />
              </>
            ) : (
              <TopBar />
            )}
          </Box>

          {/* Scrollable Main Content */}

          <Box
            sx={{
              overflowY: 'auto',
              width: { smd: '100%', lg: '719px' },
              pr: { smd: '20px', lg: '0px' },
              scrollbarWidth: 'none',
              '&::-webkit-scrollbar': {
                display: 'none',
              },
              height: '100vh',
              mt: { xs: '340px', sm: '100px' },
              px: '16px',
            }}
          >
            <MainContent>
              {[...Array(10)].map((_, index) => (
                <React.Fragment key={index}>
                  <ContentQuestionPost />
                  <ContentPollPost />
                  <ContentArticlePost />
                  <ContentMediaPost />
                  <ContextTextPost />
                </React.Fragment>
              ))}
            </MainContent>
          </Box>
        </Box>

        {/* Right Sidebar - Desktop Only */}
        {!isMobile && <RightSidebar />}

        {/* Bottom Nav - Mobile Only */}
        {isMobile && (
          <Box
            sx={{
              position: 'sticky',
              bottom: 0,
              zIndex: 10,
              backgroundColor: 'white',
            }}
          >
            <BottomNavBar />
          </Box>
        )}
      </Box>
    </Box>
  );
}
